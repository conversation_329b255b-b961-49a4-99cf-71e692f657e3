<?php

use Illuminate\Support\Facades\Route;

use App\Http\Controllers\PluginController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\UserPreferenceController;

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected Routes
Route::middleware('auth')->group(function () {
    Route::get('/', function () {
        return view('dashboard');
    })->name('dashboard');























    // User Preferences Routes
    Route::prefix('preferences')->name('preferences.')->group(function () {
        Route::get('/', [UserPreferenceController::class, 'show'])->name('show');
        Route::put('/', [UserPreferenceController::class, 'update'])->name('update');
        Route::post('/update-preference', [UserPreferenceController::class, 'updatePreference'])->name('update-preference');
        Route::post('/toggle-dark-mode', [UserPreferenceController::class, 'toggleDarkMode'])->name('toggle-dark-mode');
        Route::post('/reset', [UserPreferenceController::class, 'reset'])->name('reset');
    });

    // Plugin Management Routes
    Route::prefix('plugins')->name('plugins.')->group(function () {
        Route::get('/', [PluginController::class, 'index'])->name('index');
        Route::get('/refresh', [PluginController::class, 'refresh'])->name('refresh');
        Route::get('/api', [PluginController::class, 'api'])->name('api');
        Route::get('/check-dependencies', [PluginController::class, 'checkDependencies'])->name('check-dependencies');
        Route::get('/install', [PluginController::class, 'showInstall'])->name('install');
        Route::post('/install', [PluginController::class, 'install'])->name('install.process');
        Route::get('/{name}', [PluginController::class, 'show'])->name('show');
        Route::get('/{name}/validate', [PluginController::class, 'validatePlugin'])->name('validate');
        Route::get('/{name}/export', [PluginController::class, 'export'])->name('export');
        Route::post('/{name}/enable', [PluginController::class, 'enable'])->name('enable');
        Route::post('/{name}/disable', [PluginController::class, 'disable'])->name('disable');
        Route::post('/{name}/uninstall', [PluginController::class, 'uninstall'])->name('uninstall');
        Route::post('/{name}/seed-sample-data', [PluginController::class, 'seedSampleData'])->name('seed-sample-data');
        Route::post('/{name}/clear-all-data', [PluginController::class, 'clearAllData'])->name('clear-all-data');
        Route::post('/{name}/sync-permissions', [PluginController::class, 'syncPermissions'])->name('sync-permissions');
        Route::post('/{name}/sync-navigation', [PluginController::class, 'syncNavigation'])->name('sync-navigation');
        Route::post('/{name}/run-migrations', [PluginController::class, 'runMigrations'])->name('run-migrations');
        Route::post('/{name}/rollback-migrations', [PluginController::class, 'rollbackMigrations'])->name('rollback-migrations');
    });


});
