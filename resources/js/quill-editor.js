import Quill from 'quill';
import 'quill/dist/quill.snow.css';

/**
 * Initialize Quill editor for todo description
 */
export function initializeQuillEditor() {
    const editorContainer = document.getElementById('todoDescriptionEditor');
    const hiddenInput = document.getElementById('todoDescriptionHidden');
    
    if (!editorContainer || !hiddenInput) {
        return null;
    }

    // Custom toolbar configuration with all requested features
    const toolbarOptions = [
        // Text formatting
        ['bold', 'italic', 'underline', 'strike'],
        
        // Font size and color
        [{ 'size': ['small', false, 'large', 'huge'] }],
        [{ 'color': [] }, { 'background': [] }],
        
        // Headers
        [{ 'header': [1, 2, 3, false] }],
        
        // Lists
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        
        // Text alignment
        [{ 'align': [] }],
        
        // Links and other elements
        ['link', 'blockquote', 'code-block'],
        
        // Clear formatting
        ['clean']
    ];

    // Initialize Quill with configuration
    const quill = new Quill(editorContainer, {
        theme: 'snow',
        placeholder: 'Enter todo description (optional). You can use the formatting tools above.',
        modules: {
            toolbar: toolbarOptions,
            history: {
                delay: 1000,
                maxStack: 50,
                userOnly: true
            }
        }
    });

    // Sync content with hidden input for form submission
    quill.on('text-change', function() {
        hiddenInput.value = quill.root.innerHTML;
    });

    // Handle keyboard shortcuts
    quill.keyboard.addBinding({
        key: 'B',
        ctrlKey: true
    }, function() {
        quill.format('bold', !quill.getFormat().bold);
    });

    quill.keyboard.addBinding({
        key: 'I',
        ctrlKey: true
    }, function() {
        quill.format('italic', !quill.getFormat().italic);
    });

    quill.keyboard.addBinding({
        key: 'U',
        ctrlKey: true
    }, function() {
        quill.format('underline', !quill.getFormat().underline);
    });

    return quill;
}

/**
 * Set content in the Quill editor
 */
export function setQuillContent(quill, content) {
    if (quill && content) {
        quill.root.innerHTML = content;
    }
}

/**
 * Clear the Quill editor content
 */
export function clearQuillContent(quill) {
    if (quill) {
        quill.setContents([]);
    }
}

/**
 * Get the HTML content from Quill editor
 */
export function getQuillContent(quill) {
    return quill ? quill.root.innerHTML : '';
}
