@extends('layouts.app')

@section('title', 'Edit Product - ' . $product->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900">Edit Product</h1>
            <div class="flex space-x-3">
                <a href="{{ route('products.show', $product) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Product
                </a>
                <a href="{{ route('products.index') }}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                    All Products
                </a>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form method="POST" action="{{ route('products.update', $product) }}" class="p-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    </div>

                    <!-- Product Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            Product Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name', $product->name) }}" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-300 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">The main product name used for identification</p>
                    </div>

                    <!-- Slug -->
                    <div>
                        <label for="slug" class="block text-sm font-medium text-gray-700">URL Slug</label>
                        <input type="text" name="slug" id="slug" value="{{ old('slug', $product->slug) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('slug') border-red-300 @enderror">
                        @error('slug')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">URL-friendly identifier for this product</p>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" id="description" rows="4"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-300 @enderror">{{ old('description', $product->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Detailed description of the product</p>
                    </div>

                    <!-- Icon -->
                    <div class="md:col-span-2">
                        <label for="icon" class="block text-sm font-medium text-gray-700">Product Icon</label>
                        <select name="icon" id="icon"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('icon') border-red-300 @enderror">
                            <option value="">Select an icon...</option>
                            @foreach($icons as $iconClass => $iconLabel)
                                <option value="{{ $iconClass }}" {{ old('icon', $product->icon) === $iconClass ? 'selected' : '' }}>
                                    {{ $iconLabel }}
                                </option>
                            @endforeach
                        </select>
                        @error('icon')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Choose an icon to represent this product</p>
                    </div>

                    <!-- Target Audience -->
                    <div>
                        <label for="target_audience" class="block text-sm font-medium text-gray-700">Target Audience</label>
                        <textarea name="target_audience" id="target_audience" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('target_audience') border-red-300 @enderror">{{ old('target_audience', $product->target_audience) }}</textarea>
                        @error('target_audience')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Who is this product designed for?</p>
                    </div>

                    <!-- Use Cases -->
                    <div>
                        <label for="use_cases" class="block text-sm font-medium text-gray-700">Use Cases</label>
                        <textarea name="use_cases" id="use_cases" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('use_cases') border-red-300 @enderror">{{ old('use_cases', $product->use_cases) }}</textarea>
                        @error('use_cases')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">What problems does this product solve?</p>
                    </div>

                    <!-- Reference Links -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reference Links</label>
                        <div id="reference-links-container">
                            @php
                                $referenceLinks = old('reference_links', $product->reference_links ?? []);
                                if (empty($referenceLinks)) {
                                    $referenceLinks = [''];
                                }
                            @endphp
                            @foreach($referenceLinks as $index => $link)
                                <div class="flex mb-2 reference-link-row">
                                    <input type="url" name="reference_links[]" value="{{ $link }}"
                                           placeholder="https://example.com"
                                           class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <button type="button" onclick="removeReferenceLink(this)"
                                            class="ml-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-3 rounded">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            @endforeach
                        </div>
                        <button type="button" onclick="addReferenceLink()"
                                class="mt-2 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-plus mr-2"></i>Add Reference Link
                        </button>
                        @error('reference_links.*')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">External links related to this product (documentation, website, etc.)</p>
                    </div>

                    <!-- Status -->
                    <div class="md:col-span-2">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" 
                                   {{ old('is_active', $product->is_active) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Product is active
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Active products are available for assignment to businesses</p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('products.show', $product) }}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Update Product
                    </button>
                </div>
            </form>
        </div>

        <!-- Danger Zone -->
        @if(auth()->user()->hasPermission('manage_products'))
            <div class="bg-white shadow overflow-hidden sm:rounded-lg mt-6">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-red-900">Danger Zone</h3>
                    <p class="mt-1 max-w-2xl text-sm text-red-600">Irreversible and destructive actions</p>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Delete Product</h4>
                            <p class="text-sm text-gray-500">
                                Once you delete a product, there is no going back. Please be certain.
                                @if($product->businesses()->exists())
                                    <br><span class="text-red-600 font-medium">This product is assigned to {{ $product->businesses()->count() }} business(es) and cannot be deleted.</span>
                                @endif
                            </p>
                        </div>
                        @if(!$product->businesses()->exists())
                            <form method="POST" action="{{ route('products.destroy', $product) }}" 
                                  onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                    Delete Product
                                </button>
                            </form>
                        @else
                            <button type="button" disabled
                                    class="bg-gray-300 text-gray-500 font-bold py-2 px-4 rounded cursor-not-allowed">
                                Cannot Delete
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

<script>
function addReferenceLink() {
    const container = document.getElementById('reference-links-container');
    const newRow = document.createElement('div');
    newRow.className = 'flex mb-2 reference-link-row';
    newRow.innerHTML = `
        <input type="url" name="reference_links[]" value=""
               placeholder="https://example.com"
               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
        <button type="button" onclick="removeReferenceLink(this)"
                class="ml-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-3 rounded">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(newRow);
}

function removeReferenceLink(button) {
    const container = document.getElementById('reference-links-container');
    const rows = container.querySelectorAll('.reference-link-row');
    
    // Keep at least one row
    if (rows.length > 1) {
        button.closest('.reference-link-row').remove();
    } else {
        // Clear the input instead of removing the row
        button.closest('.reference-link-row').querySelector('input').value = '';
    }
}

// Auto-generate slug from name if slug field is empty
document.getElementById('name').addEventListener('input', function() {
    const slugField = document.getElementById('slug');
    if (!slugField.value || slugField.dataset.autoGenerated !== 'false') {
        const slug = this.value
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
        slugField.value = slug;
        slugField.dataset.autoGenerated = 'true';
    }
});

document.getElementById('slug').addEventListener('input', function() {
    this.dataset.autoGenerated = 'false';
});
</script>
@endsection
