@extends('layouts.app')

@section('title', 'Edit Document - ' . $document->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Document</h1>
                <p class="text-gray-600 mt-1">{{ $product->name }} - {{ $document->name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('products.documents.show', [$product, $document]) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Document
                </a>
                <a href="{{ route('products.documents.index', $product) }}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                    All Documents
                </a>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form method="POST" action="{{ route('products.documents.update', [$product, $document]) }}" class="p-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Document Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Document Information</h3>
                    </div>

                    <!-- Document Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            Document Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name', $document->name) }}" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-300 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">A descriptive name for this document</p>
                    </div>

                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700">
                            Category <span class="text-red-500">*</span>
                        </label>
                        <select name="category" id="category" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('category') border-red-300 @enderror">
                            @foreach($categories as $categoryKey => $categoryLabel)
                                <option value="{{ $categoryKey }}" {{ old('category', $document->category) === $categoryKey ? 'selected' : '' }}>
                                    {{ $categoryLabel }}
                                </option>
                            @endforeach
                        </select>
                        @error('category')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Choose the appropriate document category</p>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" id="description" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-300 @enderror">{{ old('description', $document->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Optional description of the document content</p>
                    </div>

                    <!-- Version -->
                    <div>
                        <label for="version" class="block text-sm font-medium text-gray-700">Version</label>
                        <input type="text" name="version" id="version" value="{{ old('version', $document->version) }}"
                               placeholder="1.0"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('version') border-red-300 @enderror">
                        @error('version')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Document version (e.g., 1.0, 2.1, etc.)</p>
                    </div>

                    <!-- Current File Info -->
                    <div class="md:col-span-2">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Current File</h4>
                        <div class="bg-gray-50 rounded-md p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="{{ $document->file_icon }}"></i>
                                </div>
                                <div class="ml-4 flex-1">
                                    <p class="text-sm font-medium text-gray-900">{{ $document->original_name }}</p>
                                    <p class="text-sm text-gray-500">
                                        {{ $document->formatted_file_size }} • 
                                        Uploaded {{ $document->upload_date->format('M d, Y') }} by {{ $document->uploader->name }}
                                    </p>
                                </div>
                                <div class="flex-shrink-0 space-x-2">
                                    @if($document->isViewableInBrowser())
                                        <a href="{{ route('products.documents.view', [$product, $document]) }}" 
                                           target="_blank"
                                           class="text-blue-600 hover:text-blue-900 text-sm">
                                            View
                                        </a>
                                    @endif
                                    <a href="{{ route('products.documents.download', [$product, $document]) }}" 
                                       class="text-green-600 hover:text-green-900 text-sm">
                                        Download
                                    </a>
                                </div>
                            </div>
                        </div>
                        <p class="mt-2 text-xs text-gray-500">
                            To replace the file, use the "Upload New Version" feature from the document detail page.
                        </p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('products.documents.show', [$product, $document]) }}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Update Document
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
