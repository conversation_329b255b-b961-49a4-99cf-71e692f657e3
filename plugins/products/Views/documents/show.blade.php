@extends('layouts.app')

@section('title', $document->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-start mb-6">
            <div class="flex items-center">
                <i class="{{ $document->file_icon }} text-4xl mr-4"></i>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $document->name }}</h1>
                    <p class="text-gray-600 mt-1">{{ $product->name }}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ $document->category_label }}
                        </span>
                        <span class="text-sm text-gray-500">v{{ $document->version }}</span>
                        @if($document->is_current_version)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Current Version
                            </span>
                        @endif
                    </div>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('products.documents.index', $product) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Documents
                </a>
                @if(auth()->user()->hasPermission('view_product_documents'))
                    @if($document->isViewableInBrowser())
                        <a href="{{ route('products.documents.view', [$product, $document]) }}" 
                           target="_blank"
                           class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-eye mr-2"></i>View
                        </a>
                    @endif
                    <a href="{{ route('products.documents.download', [$product, $document]) }}" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-download mr-2"></i>Download
                    </a>
                @endif
                @if(auth()->user()->hasPermission('manage_product_documents'))
                    <a href="{{ route('products.documents.edit', [$product, $document]) }}" 
                       class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                @endif
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Document Information -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Document Information</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about this document</p>
                    </div>
                    <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
                        <dl class="sm:divide-y sm:divide-gray-200">
                            @if($document->description)
                                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500">Description</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $document->description }}</dd>
                                </div>
                            @endif
                            <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">File Name</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $document->original_name }}</dd>
                            </div>
                            <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">File Size</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $document->formatted_file_size }}</dd>
                            </div>
                            <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">File Type</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $document->mime_type }}</dd>
                            </div>
                            <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Uploaded By</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                    {{ $document->uploader->name }} on {{ $document->upload_date->format('M d, Y \a\t g:i A') }}
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Version History -->
                @if($versions->count() > 1)
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Version History</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">All versions of this document</p>
                        </div>
                        <div class="border-t border-gray-200">
                            <ul class="divide-y divide-gray-200">
                                @foreach($versions as $version)
                                    <li class="px-4 py-4 sm:px-6 {{ $version->is_current_version ? 'bg-blue-50' : '' }}">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="{{ $version->file_icon }} mr-3"></i>
                                                <div>
                                                    <p class="text-sm font-medium text-gray-900">
                                                        Version {{ $version->version }}
                                                        @if($version->is_current_version)
                                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                Current
                                                            </span>
                                                        @endif
                                                    </p>
                                                    <p class="text-sm text-gray-500">
                                                        {{ $version->formatted_file_size }} • 
                                                        Uploaded {{ $version->upload_date->format('M d, Y') }} by {{ $version->uploader->name }}
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="flex space-x-2">
                                                @if(auth()->user()->hasPermission('view_product_documents'))
                                                    <a href="{{ route('products.documents.version-download', [$product, $document, $version->version]) }}" 
                                                       class="text-blue-600 hover:text-blue-900 text-sm">
                                                        Download
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                @if(auth()->user()->hasPermission('manage_product_documents'))
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
                        </div>
                        <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                            <div class="space-y-4">
                                <form method="POST" action="{{ route('products.documents.new-version', [$product, $document]) }}" 
                                      enctype="multipart/form-data" id="new-version-form">
                                    @csrf
                                    <div class="space-y-3">
                                        <label class="block text-sm font-medium text-gray-700">Upload New Version</label>
                                        <input type="file" name="file" id="new-version-file" 
                                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                               accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.zip,.rar">
                                        <textarea name="description" placeholder="Version notes (optional)" rows="2"
                                                  class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm"></textarea>
                                        <button type="submit" 
                                                class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                                            Upload New Version
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Document Stats -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Document Stats</h3>
                    </div>
                    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total Versions</dt>
                                <dd class="text-lg font-semibold text-gray-900">{{ $versions->count() }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">First Upload</dt>
                                <dd class="text-sm text-gray-900">{{ $versions->sortBy('upload_date')->first()->upload_date->format('M d, Y') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                <dd class="text-sm text-gray-900">{{ $document->updated_at->format('M d, Y') }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
