@extends('layouts.app')

@section('title', 'Create Product')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900">Create New Product</h1>
            <a href="{{ route('products.index') }}" 
               class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Products
            </a>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form method="POST" action="{{ route('products.store') }}" class="p-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    </div>

                    <!-- Product Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            Product Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-300 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">The main product name used for identification</p>
                    </div>

                    <!-- Slug -->
                    <div>
                        <label for="slug" class="block text-sm font-medium text-gray-700">URL Slug</label>
                        <input type="text" name="slug" id="slug" value="{{ old('slug') }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('slug') border-red-300 @enderror">
                        @error('slug')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Leave empty to auto-generate from product name</p>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" id="description" rows="4"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-300 @enderror">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Detailed description of the product</p>
                    </div>

                    <!-- Icon -->
                    <div class="md:col-span-2">
                        <label for="icon" class="block text-sm font-medium text-gray-700">Product Icon</label>
                        <select name="icon" id="icon"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('icon') border-red-300 @enderror">
                            <option value="">Select an icon...</option>
                            @foreach($icons as $iconClass => $iconLabel)
                                <option value="{{ $iconClass }}" {{ old('icon') === $iconClass ? 'selected' : '' }}>
                                    {{ $iconLabel }}
                                </option>
                            @endforeach
                        </select>
                        @error('icon')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Choose an icon to represent this product</p>
                    </div>

                    <!-- Target Audience -->
                    <div>
                        <label for="target_audience" class="block text-sm font-medium text-gray-700">Target Audience</label>
                        <textarea name="target_audience" id="target_audience" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('target_audience') border-red-300 @enderror">{{ old('target_audience') }}</textarea>
                        @error('target_audience')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Who is this product designed for?</p>
                    </div>

                    <!-- Use Cases -->
                    <div>
                        <label for="use_cases" class="block text-sm font-medium text-gray-700">Use Cases</label>
                        <textarea name="use_cases" id="use_cases" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('use_cases') border-red-300 @enderror">{{ old('use_cases') }}</textarea>
                        @error('use_cases')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">What problems does this product solve?</p>
                    </div>

                    <!-- Reference Links -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reference Links</label>
                        <div id="reference-links-container">
                            @if(old('reference_links'))
                                @foreach(old('reference_links') as $index => $link)
                                    <div class="flex mb-2 reference-link-row">
                                        <input type="url" name="reference_links[]" value="{{ $link }}"
                                               placeholder="https://example.com"
                                               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                        <button type="button" onclick="removeReferenceLink(this)"
                                                class="ml-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-3 rounded">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                @endforeach
                            @else
                                <div class="flex mb-2 reference-link-row">
                                    <input type="url" name="reference_links[]" value=""
                                           placeholder="https://example.com"
                                           class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <button type="button" onclick="removeReferenceLink(this)"
                                            class="ml-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-3 rounded">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            @endif
                        </div>
                        <button type="button" onclick="addReferenceLink()"
                                class="mt-2 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-plus mr-2"></i>Add Reference Link
                        </button>
                        @error('reference_links.*')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">External links related to this product (documentation, website, etc.)</p>
                    </div>

                    <!-- Status -->
                    <div class="md:col-span-2">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" 
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Product is active
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Active products are available for assignment to businesses</p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('products.index') }}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Create Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function addReferenceLink() {
    const container = document.getElementById('reference-links-container');
    const newRow = document.createElement('div');
    newRow.className = 'flex mb-2 reference-link-row';
    newRow.innerHTML = `
        <input type="url" name="reference_links[]" value=""
               placeholder="https://example.com"
               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
        <button type="button" onclick="removeReferenceLink(this)"
                class="ml-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-3 rounded">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(newRow);
}

function removeReferenceLink(button) {
    const container = document.getElementById('reference-links-container');
    const rows = container.querySelectorAll('.reference-link-row');
    
    // Keep at least one row
    if (rows.length > 1) {
        button.closest('.reference-link-row').remove();
    } else {
        // Clear the input instead of removing the row
        button.closest('.reference-link-row').querySelector('input').value = '';
    }
}

// Auto-generate slug from name
document.getElementById('name').addEventListener('input', function() {
    const slugField = document.getElementById('slug');
    if (!slugField.value || slugField.dataset.autoGenerated !== 'false') {
        const slug = this.value
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
        slugField.value = slug;
        slugField.dataset.autoGenerated = 'true';
    }
});

document.getElementById('slug').addEventListener('input', function() {
    this.dataset.autoGenerated = 'false';
});
</script>
@endsection
