@extends('layouts.app')

@section('title', 'Add Pricing Item - ' . $product->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Add Pricing Item</h1>
                <p class="text-gray-600 mt-1">{{ $product->name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('products.pricing.index', $product) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Pricing
                </a>
                <a href="{{ route('products.show', $product) }}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                    Back to Product
                </a>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form method="POST" action="{{ route('products.pricing.store', $product) }}" class="p-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing Item Information</h3>
                    </div>

                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            Item Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               placeholder="e.g., Marketing Messages, Monthly Subscription"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-300 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>



                    <!-- Pricing Model -->
                    <div>
                        <label for="pricing_model" class="block text-sm font-medium text-gray-700">
                            Pricing Model <span class="text-red-500">*</span>
                        </label>
                        <select name="pricing_model" id="pricing_model" required onchange="toggleUnitFields()"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('pricing_model') border-red-300 @enderror">
                            @foreach($pricingModels as $modelKey => $modelLabel)
                                <option value="{{ $modelKey }}" {{ old('pricing_model', 'fixed') === $modelKey ? 'selected' : '' }}>
                                    {{ $modelLabel }}
                                </option>
                            @endforeach
                        </select>
                        @error('pricing_model')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Choose how this item is priced</p>
                    </div>

                    <!-- Price -->
                    <div>
                        <label for="price" class="block text-sm font-medium text-gray-700">Price</label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <input type="number" name="price" id="price" value="{{ old('price') }}" 
                                   step="0.01" min="0"
                                   placeholder="0.00"
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('price') border-red-300 @enderror">
                        </div>
                        @error('price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Leave empty for "Contact for pricing"</p>
                    </div>

                    <!-- Currency -->
                    <div>
                        <label for="currency" class="block text-sm font-medium text-gray-700">
                            Currency <span class="text-red-500">*</span>
                        </label>
                        <select name="currency" id="currency" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('currency') border-red-300 @enderror">
                            @foreach($currencies as $currencyCode => $currencyLabel)
                                <option value="{{ $currencyCode }}" {{ old('currency', 'SAR') === $currencyCode ? 'selected' : '' }}>
                                    {{ $currencyLabel }}
                                </option>
                            @endforeach
                        </select>
                        @error('currency')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Unit Type (Per-Unit Pricing Only) -->
                    <div id="unit_type_field" class="hidden">
                        <label for="unit_type" class="block text-sm font-medium text-gray-700">
                            Unit Type
                        </label>
                        <select name="unit_type" id="unit_type" onchange="toggleCustomUnitField()"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('unit_type') border-red-300 @enderror">
                            <option value="">Select unit type...</option>
                            @if(isset($unitTypes))
                                @foreach($unitTypes as $unitKey => $unitLabel)
                                    <option value="{{ $unitKey }}" {{ old('unit_type') === $unitKey ? 'selected' : '' }}>
                                        {{ $unitLabel }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                        @error('unit_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">What unit is this pricing based on?</p>
                    </div>

                    <!-- Custom Unit Name (When Custom Unit is selected) -->
                    <div id="custom_unit_field" class="hidden">
                        <label for="custom_unit_name" class="block text-sm font-medium text-gray-700">
                            Custom Unit Name
                        </label>
                        <input type="text" name="custom_unit_name" id="custom_unit_name" value="{{ old('custom_unit_name') }}"
                               placeholder="e.g., per widget, per license, per seat"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('custom_unit_name') border-red-300 @enderror">
                        @error('custom_unit_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Enter the name of your custom unit</p>
                    </div>





                    <!-- Billing Cycle -->
                    <div>
                        <label for="billing_cycle" class="block text-sm font-medium text-gray-700">
                            Billing Cycle <span class="text-red-500">*</span>
                        </label>
                        <select name="billing_cycle" id="billing_cycle" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('billing_cycle') border-red-300 @enderror">
                            @foreach($billingCycles as $cycleKey => $cycleLabel)
                                <option value="{{ $cycleKey }}" {{ old('billing_cycle', 'monthly') === $cycleKey ? 'selected' : '' }}>
                                    {{ $cycleLabel }}
                                </option>
                            @endforeach
                        </select>
                        @error('billing_cycle')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" id="description" rows="3"
                                  placeholder="Detailed description of this pricing item and what it includes"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-300 @enderror">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Customer-facing description of this pricing item</p>
                    </div>





                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700">Sort Order</label>
                        <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', 0) }}"
                               min="0"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('sort_order') border-red-300 @enderror">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Lower numbers appear first</p>
                    </div>

                    <!-- Notes -->
                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                        <textarea name="notes" id="notes" rows="3"
                                  placeholder="Internal notes, conditions, or additional information for this pricing item"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('notes') border-red-300 @enderror">{{ old('notes') }}</textarea>
                        @error('notes')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Internal notes and conditions (not visible to customers)</p>
                    </div>

                    <!-- Options -->
                    <div class="md:col-span-2">
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" name="is_active" id="is_active" value="1"
                                       {{ old('is_active', true) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                    Active pricing item
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" name="is_popular" id="is_popular" value="1"
                                       {{ old('is_popular') ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_popular" class="ml-2 block text-sm text-gray-900">
                                    Mark as popular (recommended item)
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" name="include_in_quotations" id="include_in_quotations" value="1"
                                       {{ old('include_in_quotations', true) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="include_in_quotations" class="ml-2 block text-sm text-gray-900">
                                    Include in quotations
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('products.pricing.index', $product) }}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit"
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Create Pricing Item
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleUnitFields() {
    const pricingModel = document.getElementById('pricing_model').value;
    const unitTypeField = document.getElementById('unit_type_field');
    const customUnitField = document.getElementById('custom_unit_field');

    if (pricingModel === 'per_unit') {
        unitTypeField.classList.remove('hidden');
        // Check if custom unit is selected
        toggleCustomUnitField();
    } else {
        unitTypeField.classList.add('hidden');
        customUnitField.classList.add('hidden');
        // Clear values when hiding
        document.getElementById('unit_type').value = '';
        document.getElementById('custom_unit_name').value = '';
    }
}

function toggleCustomUnitField() {
    const unitType = document.getElementById('unit_type').value;
    const customUnitField = document.getElementById('custom_unit_field');

    if (unitType === 'custom') {
        customUnitField.classList.remove('hidden');
    } else {
        customUnitField.classList.add('hidden');
        // Clear custom unit name when hiding
        document.getElementById('custom_unit_name').value = '';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleUnitFields();
});
</script>
@endsection
