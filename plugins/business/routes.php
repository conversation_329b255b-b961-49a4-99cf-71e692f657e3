<?php

use Illuminate\Support\Facades\Route;
use Plugins\Business\Controllers\BusinessController;
use Plugins\Business\Controllers\ContactController;
use Plugins\Business\Controllers\DocumentController;
use Plugins\Business\Controllers\TagController;
use Plugins\Business\Controllers\ProductController;
use Plugins\Business\Controllers\BusinessTagController;
use Plugins\Business\Controllers\BusinessProductController;
use Plugins\Business\Controllers\BusinessActivityController;
use Plugins\Business\Controllers\BusinessNotificationController;

// Business Management Routes
Route::prefix('business')->name('business.')->group(function () {
    Route::get('/', [BusinessController::class, 'index'])->name('index');
    Route::get('/create', [BusinessController::class, 'create'])->name('create');
    Route::post('/', [BusinessController::class, 'store'])->name('store');
    Route::get('/{business}', [BusinessController::class, 'show'])->name('show');
    Route::get('/{business}/edit', [BusinessController::class, 'edit'])->name('edit');
    Route::put('/{business}', [BusinessController::class, 'update'])->name('update');
    Route::delete('/{business}', [BusinessController::class, 'destroy'])->name('destroy');
    
    // Business user management routes
    Route::get('/{business}/users', [BusinessController::class, 'users'])->name('users');
    Route::post('/{business}/users', [BusinessController::class, 'assignUser'])->name('assign-user');
    Route::delete('/{business}/users/{user}', [BusinessController::class, 'removeUser'])->name('remove-user');
    
    // Business reports routes
    Route::get('/{business}/reports', [BusinessController::class, 'reports'])->name('reports');

    // Contact Management Routes
    Route::prefix('/{business}/contacts')->name('contacts.')->group(function () {
        Route::get('/', [ContactController::class, 'index'])->name('index');
        Route::get('/create', [ContactController::class, 'create'])->name('create');
        Route::post('/', [ContactController::class, 'store'])->name('store');
        Route::get('/{contact}', [ContactController::class, 'show'])->name('show');
        Route::get('/{contact}/edit', [ContactController::class, 'edit'])->name('edit');
        Route::put('/{contact}', [ContactController::class, 'update'])->name('update');
        Route::delete('/{contact}', [ContactController::class, 'destroy'])->name('destroy');
        Route::patch('/{contact}/set-primary', [ContactController::class, 'setPrimary'])->name('set-primary');
    });

    // Document Management Routes
    Route::prefix('/{business}/documents')->name('documents.')->group(function () {
        Route::get('/', [DocumentController::class, 'index'])->name('index');
        Route::get('/create', [DocumentController::class, 'create'])->name('create');
        Route::post('/', [DocumentController::class, 'store'])->name('store');
        Route::delete('/{document}', [DocumentController::class, 'destroy'])->name('destroy');
        Route::get('/{document}/download', [DocumentController::class, 'download'])->name('download');
        Route::get('/{document}/view', [DocumentController::class, 'view'])->name('view');
    });

    // Business Tag Assignment Routes
    Route::prefix('/{business}/tags')->name('tags.')->group(function () {
        Route::get('/', [BusinessTagController::class, 'index'])->name('index');
        Route::post('/assign', [BusinessTagController::class, 'assign'])->name('assign');
        Route::delete('/{tag}', [BusinessTagController::class, 'remove'])->name('remove');
        Route::get('/search', [BusinessTagController::class, 'search'])->name('search');
        Route::post('/bulk-assign', [BusinessTagController::class, 'bulkAssign'])->name('bulk-assign');
        Route::delete('/{tag}/ajax', [BusinessTagController::class, 'ajaxRemove'])->name('ajax-remove');
    });

    // Business Product Assignment Routes
    Route::prefix('/{business}/products')->name('products.')->group(function () {
        Route::get('/', [BusinessProductController::class, 'index'])->name('index');
        Route::get('/create', [BusinessProductController::class, 'create'])->name('create');
        Route::post('/assign', [BusinessProductController::class, 'assign'])->name('assign');
        Route::get('/{product}/edit', [BusinessProductController::class, 'edit'])->name('edit');
        Route::put('/{product}', [BusinessProductController::class, 'update'])->name('update');
        Route::delete('/{product}', [BusinessProductController::class, 'remove'])->name('remove');
        Route::get('/search', [BusinessProductController::class, 'search'])->name('search');
    });

    // Business Activity Routes
    Route::prefix('/{business}/activities')->name('activity.')->group(function () {
        Route::get('/', [BusinessActivityController::class, 'index'])->name('index');
        Route::post('/', [BusinessActivityController::class, 'store'])->name('store');
        Route::put('/{activity}', [BusinessActivityController::class, 'update'])->name('update');
        Route::delete('/{activity}', [BusinessActivityController::class, 'destroy'])->name('destroy');

        // Comment routes
        Route::post('/{activity}/comments', [BusinessActivityController::class, 'addComment'])->name('comment.store');
        Route::put('/comments/{comment}', [BusinessActivityController::class, 'updateComment'])->name('comment.update');
        Route::delete('/comments/{comment}', [BusinessActivityController::class, 'deleteComment'])->name('comment.destroy');

        // Attachment routes
        Route::post('/{activity}/attachments', [BusinessActivityController::class, 'uploadAttachment'])->name('attachment.upload');
        Route::get('/attachments/{attachment}/download', [BusinessActivityController::class, 'downloadAttachment'])->name('attachment.download');
        Route::delete('/attachments/{attachment}', [BusinessActivityController::class, 'deleteAttachment'])->name('attachment.destroy');
    });

    // Business Notification Routes
    Route::prefix('/{business}/notifications')->name('notifications.')->group(function () {
        Route::get('/preferences', [BusinessNotificationController::class, 'show'])->name('preferences.show');
        Route::put('/preferences', [BusinessNotificationController::class, 'update'])->name('preferences.update');
        Route::post('/preferences/reset', [BusinessNotificationController::class, 'reset'])->name('preferences.reset');
        Route::post('/test', [BusinessNotificationController::class, 'test'])->name('test');
        Route::get('/history', [BusinessNotificationController::class, 'history'])->name('history');
        Route::post('/mark-read', [BusinessNotificationController::class, 'markAsRead'])->name('mark-read');
    });
});

// Tag Management Routes
Route::prefix('tags')->name('tags.')->group(function () {
    Route::get('/', [TagController::class, 'index'])->name('index');
    Route::get('/create', [TagController::class, 'create'])->name('create');
    Route::post('/', [TagController::class, 'store'])->name('store');
    Route::get('/search', [TagController::class, 'search'])->name('search');
    Route::get('/{tag}', [TagController::class, 'show'])->name('show');
    Route::get('/{tag}/edit', [TagController::class, 'edit'])->name('edit');
    Route::put('/{tag}', [TagController::class, 'update'])->name('update');
    Route::delete('/{tag}', [TagController::class, 'destroy'])->name('destroy');
});

// Product Management Routes
Route::prefix('products')->name('products.')->group(function () {
    Route::get('/', [ProductController::class, 'index'])->name('index');
    Route::get('/create', [ProductController::class, 'create'])->name('create');
    Route::post('/', [ProductController::class, 'store'])->name('store');
    Route::get('/search', [ProductController::class, 'search'])->name('search');
    Route::get('/{product}', [ProductController::class, 'show'])->name('show');
    Route::get('/{product}/edit', [ProductController::class, 'edit'])->name('edit');
    Route::put('/{product}', [ProductController::class, 'update'])->name('update');
    Route::delete('/{product}', [ProductController::class, 'destroy'])->name('destroy');
});

// Global Contacts Routes
Route::get('/contacts', [ContactController::class, 'globalIndex'])->name('contacts.index');

// Global Documents Routes
Route::get('/documents', [DocumentController::class, 'globalIndex'])->name('documents.index');
