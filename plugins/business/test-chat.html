<!DOCTYPE html>
<html>
<head>
    <title>Chat Interface Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="p-4">
        <h1 class="text-2xl font-bold mb-4">Business Chat Interface Test</h1>
        
        <!-- Test Elements -->
        <div id="activity-content" class="border rounded-lg p-4">
            <div id="messages-list" class="mb-4">
                <div class="text-center text-gray-500">Messages will appear here</div>
            </div>
            
            <div id="messages-loading" class="hidden text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p class="text-sm text-gray-500">Loading messages...</p>
            </div>
            
            <div id="messages-empty" class="hidden text-center">
                <p class="text-gray-500">No messages yet</p>
            </div>
            
            <form id="message-form" class="mt-4">
                <div class="flex space-x-2">
                    <select id="message-type-selector" class="border rounded px-2 py-1">
                        <option value="comment">💬 Comment</option>
                        <option value="chat">💬 Chat</option>
                        <option value="email">📧 Email</option>
                    </select>
                    
                    <textarea id="message-input" 
                              class="flex-1 border rounded px-3 py-2" 
                              placeholder="Type your message..."
                              rows="1"></textarea>
                    
                    <button id="send-btn" 
                            type="submit" 
                            class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                
                <input type="hidden" id="editing-message-id" value="">
                <input type="hidden" id="reply-to-message-id" value="">
                
                <div id="char-count" class="text-xs text-gray-400 mt-1">0/1000</div>
            </form>
        </div>
        
        <div class="mt-4 p-4 bg-gray-100 rounded">
            <h3 class="font-bold mb-2">Test Results:</h3>
            <div id="test-results">
                <p>✅ DOM elements found</p>
                <p id="chat-manager-status">⏳ Initializing ChatManager...</p>
            </div>
        </div>
    </div>

    <script>
        // Mock business data for testing
        const mockBusiness = { id: 1 };
        const mockUser = { id: 1 };
        
        // Test ChatManager initialization
        document.addEventListener('DOMContentLoaded', function() {
            const testResults = document.getElementById('test-results');
            const chatManagerStatus = document.getElementById('chat-manager-status');
            
            // Check if required elements exist
            const requiredElements = [
                'activity-content',
                'messages-list', 
                'message-form',
                'message-input',
                'send-btn'
            ];
            
            let allElementsFound = true;
            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                if (!element) {
                    allElementsFound = false;
                    testResults.innerHTML += `<p class="text-red-600">❌ Missing element: ${id}</p>`;
                } else {
                    testResults.innerHTML += `<p class="text-green-600">✅ Found element: ${id}</p>`;
                }
            });
            
            if (allElementsFound) {
                chatManagerStatus.innerHTML = '✅ All required DOM elements found - ChatManager can initialize';
                chatManagerStatus.className = 'text-green-600';
            } else {
                chatManagerStatus.innerHTML = '❌ Missing required DOM elements - ChatManager will fail';
                chatManagerStatus.className = 'text-red-600';
            }
            
            // Test form submission
            const messageForm = document.getElementById('message-form');
            const messageInput = document.getElementById('message-input');
            
            if (messageForm && messageInput) {
                messageForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const message = messageInput.value.trim();
                    if (message) {
                        testResults.innerHTML += `<p class="text-blue-600">📝 Form submitted with message: "${message}"</p>`;
                        messageInput.value = '';
                    }
                });
            }
        });
    </script>
</body>
</html>
