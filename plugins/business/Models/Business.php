<?php

namespace Plugins\Business\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Plugins\Business\Models\Contact;
use Plugins\Business\Models\Document;
use Plugins\Business\Models\Tag;


class Business extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'brand_name',
        'legal_name',
        'description',
        'email',
        'phone',
        'primary_phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'website',
        'website_url',
        'logo_url',
        'tax_id',
        'status',
        'churn_reason',
        'churned_at',
        'lost_reason',
        'lost_at',
        'created_by',
        // WhatsApp Business Integration
        'whatsapp_enabled',
        'meta_business_id',
        'whatsapp_id',
        'whatsapp_provider',
        'message_quality',
        'messaging_tier',
        'whatsapp_settings',
        'whatsapp_verified_at',
        'meta_business_verified',
        'whatsapp_business_verified',
        // Taqnyat Integration
        'taqnyat_id',
        'taqnyat_username',
    ];

    protected $casts = [
        'whatsapp_enabled' => 'boolean',
        'meta_business_verified' => 'boolean',
        'whatsapp_business_verified' => 'boolean',
        'whatsapp_settings' => 'array',
        'whatsapp_verified_at' => 'datetime',
        'churned_at' => 'datetime',
        'lost_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the user who created this business
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the users assigned to this business
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'business_users')
                    ->withTimestamps()
                    ->withPivot('role', 'is_active');
    }

    /**
     * Get active users assigned to this business
     */
    public function activeUsers()
    {
        return $this->users()->wherePivot('is_active', true);
    }

    /**
     * Check if business is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Scope to get only active businesses
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get full address as a string
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country
        ]);

        return implode(', ', $parts);
    }

    /**
     * Assign a user to this business
     */
    public function assignUser(User $user, string $role = 'member', bool $isActive = true)
    {
        return $this->users()->syncWithoutDetaching([
            $user->id => [
                'role' => $role,
                'is_active' => $isActive,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }

    /**
     * Remove a user from this business
     */
    public function removeUser(User $user)
    {
        return $this->users()->detach($user->id);
    }

    /**
     * Check if a user is assigned to this business
     */
    public function hasUser(User $user): bool
    {
        return $this->users()->where('user_id', $user->id)->exists();
    }

    /**
     * Get user's role in this business
     */
    public function getUserRole(User $user): ?string
    {
        $pivot = $this->users()->where('user_id', $user->id)->first()?->pivot;
        return $pivot?->role;
    }

    /**
     * Get the contacts for this business
     */
    public function contacts()
    {
        return $this->hasMany(Contact::class);
    }

    /**
     * Get the primary contact for this business
     */
    public function primaryContact()
    {
        return $this->hasOne(Contact::class)->where('is_primary', true);
    }

    /**
     * Get the documents for this business
     */
    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Get the tags for this business
     */
    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'business_tags')
                    ->withPivot('assigned_by')
                    ->withTimestamps();
    }

    /**
     * Get the products for this business
     */
    public function products()
    {
        return $this->belongsToMany(\Plugins\Products\Models\Product::class, 'business_products')
                    ->withPivot([
                        'status', 'start_date', 'end_date', 'implementation_date',
                        'contract_value', 'renewal_date', 'product_version', 'notes',
                        'custom_features', 'assigned_by'
                    ])
                    ->withTimestamps();
    }

    /**
     * Get activities for this business
     */
    public function activities()
    {
        return $this->hasMany(BusinessActivity::class);
    }

    /**
     * Get comments for this business
     */
    public function comments()
    {
        return $this->hasMany(BusinessComment::class);
    }

    /**
     * Get notification preferences for this business
     */
    public function notificationPreferences()
    {
        return $this->hasMany(BusinessNotificationPreference::class);
    }

    /**
     * Get documents by type
     */
    public function getDocumentsByType(string $type)
    {
        return $this->documents()->where('document_type', $type);
    }

    /**
     * Check if business has a specific document type
     */
    public function hasDocumentType(string $type): bool
    {
        return $this->documents()->where('document_type', $type)->exists();
    }

    /**
     * Get display name (brand name or regular name)
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->brand_name ?: $this->name;
    }

    /**
     * Get primary website URL
     */
    public function getPrimaryWebsiteAttribute(): ?string
    {
        return $this->website_url ?: $this->website;
    }

    /**
     * Get primary phone number
     */
    public function getPrimaryPhoneNumberAttribute(): ?string
    {
        return $this->primary_phone ?: $this->phone;
    }

    /**
     * Assign a tag to this business
     */
    public function assignTag(Tag $tag, User $assignedBy)
    {
        $result = $this->tags()->syncWithoutDetaching([
            $tag->id => [
                'assigned_by' => $assignedBy->id,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);

        // Log activity if tag was actually assigned (not already assigned)
        if (!empty($result['attached'])) {
            BusinessActivity::logUserAction(
                $this,
                'tag_assignment',
                'Tag assigned',
                "Tag '{$tag->name}' was assigned to this business",
                [
                    'tag_id' => $tag->id,
                    'tag_name' => $tag->name,
                    'assigned_by' => $assignedBy->id,
                ]
            );
        }

        return $result;
    }

    /**
     * Remove a tag from this business
     */
    public function removeTag(Tag $tag)
    {
        $result = $this->tags()->detach($tag->id);

        // Log activity if tag was actually removed
        if ($result > 0) {
            BusinessActivity::logUserAction(
                $this,
                'tag_removal',
                'Tag removed',
                "Tag '{$tag->name}' was removed from this business",
                [
                    'tag_id' => $tag->id,
                    'tag_name' => $tag->name,
                ]
            );
        }

        return $result;
    }

    /**
     * Assign a product to this business
     */
    public function assignProduct(\Plugins\Products\Models\Product $product, ?User $assignedBy = null, array $options = [])
    {
        $assignedBy = $assignedBy ?: Auth::user();

        // Filter options to only include columns that exist in the pivot table
        $allowedPivotColumns = [
            'status', 'start_date', 'end_date', 'implementation_date',
            'contract_value', 'renewal_date', 'product_version', 'notes',
            'custom_features'
        ];

        $filteredOptions = array_intersect_key($options, array_flip($allowedPivotColumns));

        $pivotData = array_merge([
            'assigned_by' => $assignedBy?->id,
            'status' => 'active',
            'created_at' => now(),
            'updated_at' => now(),
        ], $filteredOptions);

        $result = $this->products()->syncWithoutDetaching([
            $product->id => $pivotData
        ]);

        // Log activity if product was actually assigned (not already assigned)
        if (!empty($result['attached'])) {
            BusinessActivity::logUserAction(
                $this,
                'product_assignment',
                'Product assigned',
                "Product '{$product->name}' was assigned to this business",
                [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'assigned_by' => $assignedBy->id,
                ]
            );
        }

        return $result;
    }

    /**
     * Remove a product from this business
     */
    public function removeProduct(\Plugins\Products\Models\Product $product)
    {
        $result = $this->products()->detach($product->id);

        // Log activity if product was actually removed
        if ($result > 0) {
            BusinessActivity::logUserAction(
                $this,
                'product_removal',
                'Product removed',
                "Product '{$product->name}' was removed from this business",
                [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                ]
            );
        }

        return $result;
    }

    /**
     * Get available business statuses
     */
    public static function getStatuses(): array
    {
        return [
            'lead' => 'Lead',
            'deal' => 'Deal',
            'customer' => 'Customer',
            'partner' => 'Partner',
            'churned' => 'Churned',
            'lost' => 'Lost',
        ];
    }

    /**
     * Get available WhatsApp providers
     */
    public static function getWhatsAppProviders(): array
    {
        return [
            'taqnyat' => 'Taqnyat',
            '360dialog' => '360Dialog',
        ];
    }

    /**
     * Get available messaging tiers
     */
    public static function getMessagingTiers(): array
    {
        return [
            'tier_0' => 'Tier 0: 250K / day',
            'tier_1' => 'Tier 1: 1K / day',
            'tier_2' => 'Tier 2: 10K / day',
            'tier_3' => 'Tier 3: 100K / day',
            'tier_4' => 'Tier 4: Unlimited',
        ];
    }



    /**
     * Check if WhatsApp is enabled and configured
     */
    public function isWhatsAppConfigured(): bool
    {
        return $this->whatsapp_enabled &&
               !empty($this->whatsapp_id) &&
               !empty($this->whatsapp_provider);
    }

    /**
     * Get WhatsApp configuration status
     */
    public function getWhatsAppStatus(): string
    {
        if (!$this->whatsapp_enabled) {
            return 'disabled';
        }

        if (!$this->whatsapp_id || !$this->whatsapp_provider) {
            return 'incomplete';
        }

        if ($this->whatsapp_verified_at) {
            return 'verified';
        }

        return 'configured';
    }

    /**
     * Get WhatsApp status badge class
     */
    public function getWhatsAppStatusBadge(): array
    {
        $status = $this->getWhatsAppStatus();

        return match($status) {
            'disabled' => ['class' => 'bg-gray-100 text-gray-800', 'text' => 'Disabled'],
            'incomplete' => ['class' => 'bg-yellow-100 text-yellow-800', 'text' => 'Incomplete'],
            'configured' => ['class' => 'bg-blue-100 text-blue-800', 'text' => 'Configured'],
            'verified' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Verified'],
            default => ['class' => 'bg-gray-100 text-gray-800', 'text' => 'Unknown'],
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return static::getStatuses()[$this->status] ?? $this->status;
    }

    /**
     * Get status color for display
     */
    public function getStatusColorAttribute(): string
    {
        $colors = [
            'lead' => 'bg-blue-100 text-blue-800',
            'deal' => 'bg-yellow-100 text-yellow-800',
            'customer' => 'bg-green-100 text-green-800',
            'partner' => 'bg-purple-100 text-purple-800',
            'churned' => 'bg-red-100 text-red-800',
            'lost' => 'bg-gray-100 text-gray-800',
        ];

        return $colors[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    /**
     * Check if business is churned
     */
    public function isChurned(): bool
    {
        return $this->status === 'churned';
    }

    /**
     * Check if business is active customer
     */
    public function isActiveCustomer(): bool
    {
        return in_array($this->status, ['customer', 'partner']);
    }

    /**
     * Scope to filter by status
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get active customers
     */
    public function scopeActiveCustomers($query)
    {
        return $query->whereIn('status', ['customer', 'partner']);
    }

    /**
     * Get logo URL or generate fallback
     */
    public function getLogoUrlAttribute($value): string
    {
        if ($value) {
            return $value;
        }

        // Generate fallback logo
        return \Plugins\Business\Services\LogoFetchService::generateFallbackLogo($this->name);
    }

    /**
     * Fetch and update logo from website
     */
    public function fetchLogoFromWebsite(): bool
    {
        $websiteUrl = $this->primary_website;

        if (!$websiteUrl) {
            return false;
        }

        $logoUrl = \Plugins\Business\Services\LogoFetchService::fetchLogoFromWebsite($websiteUrl);

        if ($logoUrl) {
            $this->update(['logo_url' => $logoUrl]);
            return true;
        }

        return false;
    }
}
