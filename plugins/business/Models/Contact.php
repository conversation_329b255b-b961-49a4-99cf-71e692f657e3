<?php

namespace Plugins\Business\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Contact extends Model
{
    use HasFactory;

    protected $table = 'business_contacts';

    protected $fillable = [
        'business_id',
        'name',
        'position',
        'department',
        'email',
        'phone',
        'is_primary',
        'notes',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the business that owns this contact
     */
    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Scope to get primary contacts
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Scope to get contacts with email
     */
    public function scopeWithEmail($query)
    {
        return $query->whereNotNull('email');
    }

    /**
     * Scope to get contacts with phone
     */
    public function scopeWithPhone($query)
    {
        return $query->whereNotNull('phone');
    }

    /**
     * Get formatted phone number
     */
    public function getFormattedPhoneAttribute(): ?string
    {
        if (!$this->phone) {
            return null;
        }

        // Basic phone formatting - can be enhanced based on requirements
        $phone = preg_replace('/[^0-9]/', '', $this->phone);
        
        if (strlen($phone) === 10) {
            return sprintf('(%s) %s-%s', 
                substr($phone, 0, 3),
                substr($phone, 3, 3),
                substr($phone, 6)
            );
        }

        return $this->phone;
    }

    /**
     * Get full contact info as string
     */
    public function getFullContactInfoAttribute(): string
    {
        $info = [$this->name];
        
        if ($this->position) {
            $info[] = $this->position;
        }
        
        if ($this->department) {
            $info[] = "({$this->department})";
        }

        return implode(' - ', $info);
    }

    /**
     * Check if contact has complete information
     */
    public function isComplete(): bool
    {
        return !empty($this->name) && 
               (!empty($this->email) || !empty($this->phone));
    }

    /**
     * Set as primary contact (and unset others)
     */
    public function setAsPrimary(): void
    {
        // First, unset all other primary contacts for this business
        static::where('business_id', $this->business_id)
              ->where('id', '!=', $this->id)
              ->update(['is_primary' => false]);

        // Set this contact as primary
        $this->update(['is_primary' => true]);
    }
}
