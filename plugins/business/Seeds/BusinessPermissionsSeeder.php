<?php

namespace Plugins\Business\Seeds;

use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class BusinessPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create business-related permissions
        $permissions = [
            [
                'name' => 'manage_businesses',
                'display_name' => 'Manage Businesses',
                'description' => 'Create, edit, and delete business entities'
            ],
            [
                'name' => 'view_businesses',
                'display_name' => 'View Businesses',
                'description' => 'Read-only access to business data'
            ],
            [
                'name' => 'manage_business_users',
                'display_name' => 'Manage Business Users',
                'description' => 'Assign users to businesses and manage their roles'
            ],
            [
                'name' => 'view_business_reports',
                'display_name' => 'View Business Reports',
                'description' => 'Access business analytics and reports'
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission['name']], $permission);
        }

        // Assign all business permissions to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $businessPermissions = Permission::whereIn('name', [
                'manage_businesses',
                'view_businesses',
                'manage_business_users',
                'view_business_reports'
            ])->get();

            foreach ($businessPermissions as $permission) {
                if (!$adminRole->permissions->contains($permission)) {
                    $adminRole->permissions()->attach($permission);
                }
            }
        }

        // Optionally assign view permissions to editor role
        $editorRole = Role::where('name', 'editor')->first();
        if ($editorRole) {
            $viewPermissions = Permission::whereIn('name', [
                'view_businesses',
                'view_business_reports'
            ])->get();

            foreach ($viewPermissions as $permission) {
                if (!$editorRole->permissions->contains($permission)) {
                    $editorRole->permissions()->attach($permission);
                }
            }
        }
    }
}
