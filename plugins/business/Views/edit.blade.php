@extends('layouts.app')

@section('title', 'Edit Business')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900">Edit Business: {{ $business->name }}</h1>
            <div class="flex space-x-3">
                <a href="{{ route('business.show', $business) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    View Business
                </a>
                <a href="{{ route('business.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Businesses
                </a>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form method="POST" action="{{ route('business.update', $business) }}" class="p-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    </div>

                    <!-- Business Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            Business Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name', $business->name) }}" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-300 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">The main business name used for identification</p>
                    </div>

                    <!-- Brand Name -->
                    <div>
                        <label for="brand_name" class="block text-sm font-medium text-gray-700">Brand Name</label>
                        <input type="text" name="brand_name" id="brand_name" value="{{ old('brand_name', $business->brand_name) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('brand_name') border-red-300 @enderror">
                        @error('brand_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Marketing or public-facing brand name (if different from business name)</p>
                    </div>

                    <!-- Legal Name -->
                    <div>
                        <label for="legal_name" class="block text-sm font-medium text-gray-700">Legal Name</label>
                        <input type="text" name="legal_name" id="legal_name" value="{{ old('legal_name', $business->legal_name) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('legal_name') border-red-300 @enderror">
                        @error('legal_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Official legal entity name (if different from business name)</p>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" name="email" id="email" value="{{ old('email', $business->email) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('email') border-red-300 @enderror">
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" id="description" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-300 @enderror">{{ old('description', $business->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Taqnyat Integration -->
                    <div class="md:col-span-2 mt-6">
                        <div class="border-t border-gray-200 pt-6">
                            <div class="mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Taqnyat Integration</h3>
                                <p class="mt-1 text-sm text-gray-600">
                                    Configure Taqnyat SMS service integration for this business
                                </p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Taqnyat ID -->
                                <div>
                                    <label for="taqnyat_id" class="block text-sm font-medium text-gray-700">Taqnyat ID</label>
                                    <input type="text" name="taqnyat_id" id="taqnyat_id"
                                           value="{{ old('taqnyat_id', $business->taqnyat_id) }}"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                                           placeholder="Enter Taqnyat account ID">
                                    <p class="mt-1 text-xs text-gray-500">Your Taqnyat account identifier</p>
                                    @error('taqnyat_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Taqnyat Username -->
                                <div>
                                    <label for="taqnyat_username" class="block text-sm font-medium text-gray-700">Taqnyat Username</label>
                                    <input type="text" name="taqnyat_username" id="taqnyat_username"
                                           value="{{ old('taqnyat_username', $business->taqnyat_username) }}"
                                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                                           placeholder="Enter Taqnyat username">
                                    <p class="mt-1 text-xs text-gray-500">Your Taqnyat account username</p>
                                    @error('taqnyat_username')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                    </div>

                    <!-- Primary Phone -->
                    <div>
                        <label for="primary_phone" class="block text-sm font-medium text-gray-700">Primary Phone</label>
                        <input type="tel" name="primary_phone" id="primary_phone" value="{{ old('primary_phone', $business->primary_phone) }}"
                               placeholder="(*************"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('primary_phone') border-red-300 @enderror">
                        @error('primary_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Main business phone number</p>
                    </div>

                    <!-- Secondary Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">Secondary Phone</label>
                        <input type="tel" name="phone" id="phone" value="{{ old('phone', $business->phone) }}"
                               placeholder="(*************"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('phone') border-red-300 @enderror">
                        @error('phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Additional phone number (optional)</p>
                    </div>

                    <!-- Primary Website -->
                    <div>
                        <label for="website_url" class="block text-sm font-medium text-gray-700">Primary Website</label>
                        <input type="url" name="website_url" id="website_url" value="{{ old('website_url', $business->website_url) }}"
                               placeholder="https://www.example.com"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('website_url') border-red-300 @enderror">
                        @error('website_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Main business website URL</p>
                    </div>

                    <!-- Secondary Website -->
                    <div>
                        <label for="website" class="block text-sm font-medium text-gray-700">Secondary Website</label>
                        <input type="url" name="website" id="website" value="{{ old('website', $business->website) }}"
                               placeholder="https://www.example.com"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('website') border-red-300 @enderror">
                        @error('website')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Additional website URL (optional)</p>
                    </div>

                    <!-- Address Information -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Address Information</h3>
                    </div>

                    <!-- Address -->
                    <div class="md:col-span-2">
                        <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                        <textarea name="address" id="address" rows="2"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('address') border-red-300 @enderror">{{ old('address', $business->address) }}</textarea>
                        @error('address')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- City -->
                    <div>
                        <label for="city" class="block text-sm font-medium text-gray-700">City</label>
                        <input type="text" name="city" id="city" value="{{ old('city', $business->city) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('city') border-red-300 @enderror">
                        @error('city')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- State -->
                    <div>
                        <label for="state" class="block text-sm font-medium text-gray-700">State/Province</label>
                        <input type="text" name="state" id="state" value="{{ old('state', $business->state) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('state') border-red-300 @enderror">
                        @error('state')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Country -->
                    <div>
                        <label for="country" class="block text-sm font-medium text-gray-700">Country</label>
                        <input type="text" name="country" id="country" value="{{ old('country', $business->country) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('country') border-red-300 @enderror">
                        @error('country')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Postal Code -->
                    <div>
                        <label for="postal_code" class="block text-sm font-medium text-gray-700">Postal Code</label>
                        <input type="text" name="postal_code" id="postal_code" value="{{ old('postal_code', $business->postal_code) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('postal_code') border-red-300 @enderror">
                        @error('postal_code')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Additional Information -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
                    </div>

                    <!-- Tax ID -->
                    <div>
                        <label for="tax_id" class="block text-sm font-medium text-gray-700">Tax ID</label>
                        <input type="text" name="tax_id" id="tax_id" value="{{ old('tax_id', $business->tax_id) }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('tax_id') border-red-300 @enderror">
                        @error('tax_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Business Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700">Business Status</label>
                        <select name="status" id="status"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('status') border-red-300 @enderror">
                            @foreach($statuses as $key => $label)
                                <option value="{{ $key }}" {{ old('status', $business->status) === $key ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Churn Reason (conditional) -->
                    <div id="churn-reason-section" class="{{ old('status', $business->status) === 'churned' ? '' : 'hidden' }}">
                        <label for="churn_reason" class="block text-sm font-medium text-gray-700">Churn Reason</label>
                        <textarea name="churn_reason" id="churn_reason" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('churn_reason') border-red-300 @enderror">{{ old('churn_reason', $business->churn_reason) }}</textarea>
                        @error('churn_reason')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">
                            Please explain why this business churned
                        </p>
                    </div>

                    <!-- Churn Date (conditional) -->
                    <div id="churn-date-section" class="{{ old('status', $business->status) === 'churned' ? '' : 'hidden' }}">
                        <label for="churned_at" class="block text-sm font-medium text-gray-700">Churn Date</label>
                        <input type="datetime-local" name="churned_at" id="churned_at"
                               value="{{ old('churned_at', $business->churned_at ? $business->churned_at->format('Y-m-d\TH:i') : '') }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('churned_at') border-red-300 @enderror">
                        @error('churned_at')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">
                            When did this business churn?
                        </p>
                    </div>

                    <!-- Lost Reason (conditional) -->
                    <div id="lost-reason-section" class="{{ old('status', $business->status) === 'lost' ? '' : 'hidden' }}">
                        <label for="lost_reason" class="block text-sm font-medium text-gray-700">Lost Reason</label>
                        <textarea name="lost_reason" id="lost_reason" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('lost_reason') border-red-300 @enderror">{{ old('lost_reason', $business->lost_reason) }}</textarea>
                        @error('lost_reason')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">
                            Please explain why this business was lost
                        </p>
                    </div>

                    <!-- Lost Date (conditional) -->
                    <div id="lost-date-section" class="{{ old('status', $business->status) === 'lost' ? '' : 'hidden' }}">
                        <label for="lost_at" class="block text-sm font-medium text-gray-700">Lost Date</label>
                        <input type="datetime-local" name="lost_at" id="lost_at"
                               value="{{ old('lost_at', $business->lost_at ? $business->lost_at->format('Y-m-d\TH:i') : '') }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('lost_at') border-red-300 @enderror">
                        @error('lost_at')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">
                            When was this business lost?
                        </p>
                    </div>

                    <!-- Tags Section -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Tags & Products</h3>
                    </div>

                    <!-- Tags -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto border border-gray-200 rounded-md p-4">
                            @foreach($tags as $tag)
                                <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <input type="checkbox" name="tag_ids[]" value="{{ $tag->id }}"
                                           {{ in_array($tag->id, old('tag_ids', $assignedTagIds)) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <div class="ml-3 flex items-center">
                                        <div class="w-3 h-3 rounded-full mr-2" style="background-color: {{ $tag->color }};"></div>
                                        <span class="text-sm text-gray-900">{{ $tag->name }}</span>
                                    </div>
                                </label>
                            @endforeach
                        </div>
                        @error('tag_ids')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <div class="mt-2 flex justify-between items-center">
                            <p class="text-xs text-gray-500">
                                Select tags to categorize this business
                            </p>
                            @if(auth()->user()->hasPermission('manage_businesses'))
                                <button type="button" onclick="openCreateTagModal()"
                                        class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    <i class="fas fa-plus mr-1"></i>Create New Tag
                                </button>
                            @endif
                        </div>
                    </div>

                    <!-- Products -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Products</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto border border-gray-200 rounded-md p-4">
                            @foreach($products as $product)
                                <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <input type="checkbox" name="product_ids[]" value="{{ $product->id }}"
                                           {{ in_array($product->id, old('product_ids', $assignedProductIds)) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <div class="ml-3 flex items-center">
                                        <i class="{{ $product->icon }} text-gray-600 mr-2"></i>
                                        <span class="text-sm text-gray-900">{{ $product->name }}</span>
                                    </div>
                                </label>
                            @endforeach
                        </div>
                        @error('product_ids')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">
                            Select products/services for this business
                        </p>
                    </div>
                </div>

                <!-- WhatsApp Business Integration -->
                <div class="mt-8">
                    <div class="border-t border-gray-200 pt-8">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">WhatsApp Business Integration</h3>
                                <p class="mt-1 text-sm text-gray-600">
                                    Configure WhatsApp Business API integration for this business
                                </p>
                            </div>
                            <div class="flex items-center">
                                <input type="hidden" name="whatsapp_enabled" value="0">
                                <input type="checkbox" id="whatsapp_enabled" name="whatsapp_enabled" value="1"
                                       {{ old('whatsapp_enabled', $business->whatsapp_enabled) ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <label for="whatsapp_enabled" class="ml-2 text-sm text-gray-700">Enable WhatsApp</label>
                            </div>
                        </div>

                        <div id="whatsapp-fields" class="grid grid-cols-1 md:grid-cols-2 gap-6 {{ old('whatsapp_enabled', $business->whatsapp_enabled) ? '' : 'hidden' }}">
                            <!-- Meta Business ID -->
                            <div>
                                <label for="meta_business_id" class="block text-sm font-medium text-gray-700">Meta Business ID</label>
                                <input type="text" name="meta_business_id" id="meta_business_id"
                                       value="{{ old('meta_business_id', $business->meta_business_id) }}"
                                       class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                                       placeholder="Enter Meta Business Account ID">
                                <p class="mt-1 text-xs text-gray-500">Your Facebook/Meta Business Account identifier</p>
                                @error('meta_business_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- WhatsApp ID -->
                            <div>
                                <label for="whatsapp_id" class="block text-sm font-medium text-gray-700">WhatsApp Business ID</label>
                                <input type="text" name="whatsapp_id" id="whatsapp_id"
                                       value="{{ old('whatsapp_id', $business->whatsapp_id) }}"
                                       class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                                       placeholder="Enter WhatsApp Business Account ID">
                                <p class="mt-1 text-xs text-gray-500">Your WhatsApp Business Account identifier</p>
                                @error('whatsapp_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- WhatsApp Provider -->
                            <div>
                                <label for="whatsapp_provider" class="block text-sm font-medium text-gray-700">WhatsApp Provider</label>
                                <select name="whatsapp_provider" id="whatsapp_provider"
                                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <option value="">Select Provider</option>
                                    @foreach(\Plugins\Business\Models\Business::getWhatsAppProviders() as $value => $label)
                                        <option value="{{ $value }}" {{ old('whatsapp_provider', $business->whatsapp_provider) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                <p class="mt-1 text-xs text-gray-500">Choose your WhatsApp Business API provider</p>
                                @error('whatsapp_provider')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Message Quality -->
                            <div>
                                <label for="message_quality" class="block text-sm font-medium text-gray-700">Message Quality Rating</label>
                                <input type="text" name="message_quality" id="message_quality"
                                       value="{{ old('message_quality', $business->message_quality) }}"
                                       class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                                       placeholder="e.g., High, Medium, Low">
                                <p class="mt-1 text-xs text-gray-500">Current message quality rating from WhatsApp</p>
                                @error('message_quality')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Messaging Tier -->
                            <div>
                                <label for="messaging_tier" class="block text-sm font-medium text-gray-700">Messaging Tier</label>
                                <select name="messaging_tier" id="messaging_tier"
                                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <option value="">Select Tier</option>
                                    @foreach(\Plugins\Business\Models\Business::getMessagingTiers() as $value => $label)
                                        <option value="{{ $value }}" {{ old('messaging_tier', $business->messaging_tier) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                <p class="mt-1 text-xs text-gray-500">Current WhatsApp Business messaging tier</p>
                                @error('messaging_tier')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Meta Business Verified -->
                            <div>
                                <div class="flex items-center">
                                    <input type="hidden" name="meta_business_verified" value="0">
                                    <input type="checkbox" id="meta_business_verified" name="meta_business_verified" value="1"
                                           {{ old('meta_business_verified', $business->meta_business_verified) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <label for="meta_business_verified" class="ml-2 text-sm text-gray-700">Meta Business Verified</label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Mark if Meta Business account is verified</p>
                                @error('meta_business_verified')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- WhatsApp Business Verified -->
                            <div>
                                <div class="flex items-center">
                                    <input type="hidden" name="whatsapp_business_verified" value="0">
                                    <input type="checkbox" id="whatsapp_business_verified" name="whatsapp_business_verified" value="1"
                                           {{ old('whatsapp_business_verified', $business->whatsapp_business_verified) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <label for="whatsapp_business_verified" class="ml-2 text-sm text-gray-700">WhatsApp Business Verified</label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Mark if WhatsApp Business account is verified</p>
                                @error('whatsapp_business_verified')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- WhatsApp Status Display -->
                            @if($business->exists)
                                <div class="md:col-span-2">
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-900">WhatsApp Integration Status</h4>
                                                <p class="text-sm text-gray-600">Current configuration status</p>
                                            </div>
                                            <div>
                                                @php $statusBadge = $business->getWhatsAppStatusBadge(); @endphp
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusBadge['class'] }}">
                                                    {{ $statusBadge['text'] }}
                                                </span>
                                            </div>
                                        </div>
                                        @if($business->whatsapp_verified_at)
                                            <p class="mt-2 text-xs text-green-600">
                                                <i class="fas fa-check-circle mr-1"></i>
                                                Verified on {{ $business->whatsapp_verified_at->format('M j, Y \a\t g:i A') }}
                                            </p>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>





                <!-- Submit Buttons -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('business.show', $business) }}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Update Business
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Tag Modal -->
<div id="createTagModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Create New Tag</h3>
                <button type="button" onclick="closeCreateTagModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createTagForm">
                @csrf
                <div class="mb-4">
                    <label for="tag_name" class="block text-sm font-medium text-gray-700 mb-2">Tag Name</label>
                    <input type="text" id="tag_name" name="name" required
                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           placeholder="Enter tag name">
                </div>
                <div class="mb-4">
                    <label for="tag_color" class="block text-sm font-medium text-gray-700 mb-2">Tag Color</label>
                    <div class="flex space-x-2">
                        <input type="color" id="tag_color" name="color" value="#3B82F6"
                               class="w-12 h-10 rounded border border-gray-300">
                        <input type="text" id="tag_color_text" value="#3B82F6"
                               class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                               placeholder="#3B82F6">
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeCreateTagModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Create Tag
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Tag creation modal functions
function openCreateTagModal() {
    document.getElementById('createTagModal').classList.remove('hidden');
}

function closeCreateTagModal() {
    document.getElementById('createTagModal').classList.add('hidden');
    document.getElementById('createTagForm').reset();
}

// Handle tag creation
document.getElementById('createTagForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('{{ route("tags.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add new tag to the tags list
            const tagsContainer = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
            const newTagHtml = `
                <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                    <input type="checkbox" name="tag_ids[]" value="${data.tag.id}" checked
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <div class="ml-3 flex items-center">
                        <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${data.tag.color};"></div>
                        <span class="text-sm text-gray-900">${data.tag.name}</span>
                    </div>
                </label>
            `;
            tagsContainer.insertAdjacentHTML('beforeend', newTagHtml);

            closeCreateTagModal();

            // Show success message
            const successMessage = document.createElement('div');
            successMessage.className = 'mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative';
            successMessage.innerHTML = `<span class="block sm:inline">Tag "${data.tag.name}" created and selected successfully!</span>`;
            document.querySelector('.max-w-7xl').insertBefore(successMessage, document.querySelector('.bg-white.shadow'));

            setTimeout(() => {
                successMessage.remove();
            }, 5000);
        } else {
            alert('Error creating tag: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating tag. Please try again.');
    });
});

// Sync color picker and text input
document.getElementById('tag_color').addEventListener('change', function() {
    document.getElementById('tag_color_text').value = this.value;
});

document.getElementById('tag_color_text').addEventListener('change', function() {
    document.getElementById('tag_color').value = this.value;
});

document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status');
    const churnReasonSection = document.getElementById('churn-reason-section');
    const churnDateSection = document.getElementById('churn-date-section');
    const lostReasonSection = document.getElementById('lost-reason-section');
    const lostDateSection = document.getElementById('lost-date-section');

    function toggleConditionalFields() {
        // Handle churn fields
        if (statusSelect.value === 'churned') {
            churnReasonSection.classList.remove('hidden');
            churnDateSection.classList.remove('hidden');
        } else {
            churnReasonSection.classList.add('hidden');
            churnDateSection.classList.add('hidden');
        }

        // Handle lost fields
        if (statusSelect.value === 'lost') {
            lostReasonSection.classList.remove('hidden');
            lostDateSection.classList.remove('hidden');
        } else {
            lostReasonSection.classList.add('hidden');
            lostDateSection.classList.add('hidden');
        }
    }

    statusSelect.addEventListener('change', toggleConditionalFields);
    toggleConditionalFields(); // Initial check

    // WhatsApp fields toggle
    const whatsappEnabledCheckbox = document.getElementById('whatsapp_enabled');
    const whatsappFields = document.getElementById('whatsapp-fields');

    function toggleWhatsAppFields() {
        if (whatsappEnabledCheckbox.checked) {
            whatsappFields.classList.remove('hidden');
        } else {
            whatsappFields.classList.add('hidden');
        }
    }

    whatsappEnabledCheckbox.addEventListener('change', toggleWhatsAppFields);
    toggleWhatsAppFields(); // Initial check
});
</script>
@endsection
