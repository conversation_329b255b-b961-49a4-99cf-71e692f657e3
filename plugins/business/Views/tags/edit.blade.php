@extends('layouts.app')

@section('title', 'Edit Tag')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Tag</h1>
                <p class="text-gray-600 mt-1">Update tag information</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('tags.show', $tag) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    View Tag
                </a>
                <a href="{{ route('tags.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Tags
                </a>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form method="POST" action="{{ route('tags.update', $tag) }}" class="p-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Tag Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            Tag Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name', $tag->name) }}" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-300 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Color -->
                    <div>
                        <label for="color" class="block text-sm font-medium text-gray-700">
                            Color <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 flex items-center space-x-3">
                            <input type="color" name="color" id="color" value="{{ old('color', $tag->color) }}" required
                                   class="h-10 w-16 border border-gray-300 rounded-md @error('color') border-red-300 @enderror">
                            <input type="text" id="color-hex" value="{{ old('color', $tag->color) }}" readonly
                                   class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-gray-50">
                        </div>
                        @error('color')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Choose a color for this tag</p>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" id="description" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-300 @enderror">{{ old('description', $tag->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">
                            Optional description for this tag
                        </p>
                    </div>

                    <!-- Status -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $tag->is_active) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="is_active" class="ml-2 text-sm text-gray-700">
                                Active
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">
                            Active tags can be assigned to businesses
                        </p>
                    </div>

                    <!-- Tag Preview -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tag Preview</label>
                        <div class="flex items-center p-3 border border-gray-200 rounded-md">
                            <div id="color-preview" class="w-4 h-4 rounded-full mr-3" style="background-color: {{ old('color', $tag->color) }};"></div>
                            <span id="name-preview" class="text-sm font-medium">{{ old('name', $tag->name) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('tags.show', $tag) }}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Update Tag
                    </button>
                </div>
            </form>
        </div>

        <!-- Tag History -->
        <div class="mt-6 bg-gray-50 border border-gray-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-gray-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-gray-800">Tag History</h3>
                    <div class="mt-2 text-sm text-gray-600">
                        <p>Created: {{ $tag->created_at->format('M d, Y \a\t g:i A') }}</p>
                        @if($tag->updated_at != $tag->created_at)
                            <p>Last updated: {{ $tag->updated_at->format('M d, Y \a\t g:i A') }}</p>
                        @endif
                        @if($tag->creator)
                            <p>Created by: {{ $tag->creator->name }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Statistics -->
        <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Usage Statistics</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">How this tag is being used</p>
            </div>
            <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ $tag->businesses()->count() }}</div>
                        <div class="text-sm text-gray-500">Businesses Tagged</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">{{ $tag->created_at->diffForHumans() }}</div>
                        <div class="text-sm text-gray-500">Age</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const colorInput = document.getElementById('color');
    const colorHex = document.getElementById('color-hex');
    const colorPreview = document.getElementById('color-preview');
    const nameInput = document.getElementById('name');
    const namePreview = document.getElementById('name-preview');

    // Update color preview and hex value
    colorInput.addEventListener('input', function() {
        const color = this.value;
        colorHex.value = color;
        colorPreview.style.backgroundColor = color;
    });

    // Update name preview
    nameInput.addEventListener('input', function() {
        namePreview.textContent = this.value || 'Tag Name';
    });
});
</script>
@endsection
