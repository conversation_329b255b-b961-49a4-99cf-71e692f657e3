@extends('layouts.app')

@section('title', 'Business Documents')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Documents: {{ $business->name }}</h1>
                <p class="text-gray-600 mt-1">Manage documents for this business</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('business.show', $business) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Business
                </a>
                @if(auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('business.documents.create', $business) }}" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Upload Document
                    </a>
                @endif
            </div>
        </div>

        <!-- Document Type Filter -->
        <div class="bg-white shadow rounded-lg p-4 mb-6">
            <div class="flex flex-wrap gap-2">
                <span class="text-sm font-medium text-gray-700 mr-4">Filter by type:</span>
                <a href="{{ route('business.documents.index', $business) }}" 
                   class="px-3 py-1 text-xs font-medium rounded-full {{ !request('type') ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' }}">
                    All ({{ $documents->count() }})
                </a>
                @foreach($documentTypes as $key => $label)
                    @php $count = $documents->where('document_type', $key)->count(); @endphp
                    @if($count > 0)
                        <a href="{{ route('business.documents.index', $business) }}?type={{ $key }}" 
                           class="px-3 py-1 text-xs font-medium rounded-full {{ request('type') === $key ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' }}">
                            {{ $label }} ({{ $count }})
                        </a>
                    @endif
                @endforeach
            </div>
        </div>

        <!-- Documents List -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            @if($documents->count() > 0)
                @php
                    $filteredDocuments = request('type') 
                        ? $documents->where('document_type', request('type')) 
                        : $documents;
                @endphp
                
                @if($filteredDocuments->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                        @foreach($filteredDocuments as $document)
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center">
                                        <i class="{{ $document->file_icon }} text-2xl text-gray-600 mr-3"></i>
                                        <div>
                                            <h3 class="text-sm font-medium text-gray-900 truncate">
                                                {{ $document->original_name }}
                                            </h3>
                                            <p class="text-xs text-gray-500">{{ $document->document_type_label }}</p>
                                        </div>
                                    </div>
                                    <div class="flex space-x-1">
                                        @if($document->isImage() || $document->isPdf())
                                            <a href="{{ route('business.documents.view', [$business, $document]) }}" 
                                               target="_blank"
                                               class="text-blue-600 hover:text-blue-800 text-xs">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        @endif
                                        <a href="{{ route('business.documents.download', [$business, $document]) }}" 
                                           class="text-green-600 hover:text-green-800 text-xs">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        @if(auth()->user()->hasPermission('manage_businesses'))
                                            <form method="POST" action="{{ route('business.documents.destroy', [$business, $document]) }}" 
                                                  onsubmit="return confirm('Are you sure you want to delete this document?')" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-800 text-xs">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </div>
                                
                                @if($document->description)
                                    <p class="text-xs text-gray-600 mb-3">{{ $document->description }}</p>
                                @endif
                                
                                <div class="text-xs text-gray-500 space-y-1">
                                    <div class="flex justify-between">
                                        <span>Size:</span>
                                        <span>{{ $document->formatted_file_size }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Uploaded:</span>
                                        <span>{{ $document->upload_date->format('M d, Y') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>By:</span>
                                        <span>{{ $document->uploader->name ?? 'Unknown' }}</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <div class="text-gray-500 text-lg">No documents found for the selected type.</div>
                        <a href="{{ route('business.documents.index', $business) }}" 
                           class="mt-2 text-blue-600 hover:text-blue-800">
                            View all documents
                        </a>
                    </div>
                @endif
            @else
                <div class="text-center py-12">
                    <div class="text-gray-500 text-lg">No documents found for this business.</div>
                    @if(auth()->user()->hasPermission('manage_businesses'))
                        <a href="{{ route('business.documents.create', $business) }}" 
                           class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Upload First Document
                        </a>
                    @endif
                </div>
            @endif
        </div>

        <!-- Document Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Documents</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $documents->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file-pdf text-red-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">PDF Documents</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $documents->where('mime_type', 'application/pdf')->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-image text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Images</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $documents->filter(fn($doc) => str_starts_with($doc->mime_type, 'image/'))->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-hdd text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Size</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    @php
                                        $totalSize = $documents->sum('file_size');
                                        if ($totalSize >= 1048576) {
                                            echo number_format($totalSize / 1048576, 1) . ' MB';
                                        } elseif ($totalSize >= 1024) {
                                            echo number_format($totalSize / 1024, 1) . ' KB';
                                        } else {
                                            echo $totalSize . ' bytes';
                                        }
                                    @endphp
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
