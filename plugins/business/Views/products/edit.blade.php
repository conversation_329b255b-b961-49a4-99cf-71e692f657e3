@extends('layouts.app')

@section('title', 'Edit Product')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Product</h1>
                <p class="text-gray-600 mt-1">Update product information</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('products.show', $product) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    View Product
                </a>
                <a href="{{ route('products.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Products
                </a>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form method="POST" action="{{ route('products.update', $product) }}" class="p-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Product Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            Product Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name', $product->name) }}" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-300 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Icon -->
                    <div>
                        <label for="icon" class="block text-sm font-medium text-gray-700">
                            Icon <span class="text-red-500">*</span>
                        </label>
                        <select name="icon" id="icon" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('icon') border-red-300 @enderror">
                            @foreach($availableIcons as $iconClass => $label)
                                <option value="{{ $iconClass }}" {{ old('icon', $product->icon) === $iconClass ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @error('icon')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $product->is_active) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="is_active" class="ml-2 text-sm text-gray-700">
                                Active
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">
                            Active products can be assigned to businesses
                        </p>
                    </div>

                    <!-- Icon Preview -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Icon Preview</label>
                        <div class="flex items-center p-3 border border-gray-200 rounded-md">
                            <i id="icon-preview" class="{{ old('icon', $product->icon) }} text-2xl text-gray-600 mr-3"></i>
                            <span id="icon-label">{{ $product->icon_label }}</span>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('products.show', $product) }}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Update Product
                    </button>
                </div>
            </form>
        </div>

        <!-- Product History -->
        <div class="mt-6 bg-gray-50 border border-gray-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-gray-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-gray-800">Product History</h3>
                    <div class="mt-2 text-sm text-gray-600">
                        <p>Created: {{ $product->created_at->format('M d, Y \a\t g:i A') }}</p>
                        @if($product->updated_at != $product->created_at)
                            <p>Last updated: {{ $product->updated_at->format('M d, Y \a\t g:i A') }}</p>
                        @endif
                        @if($product->creator)
                            <p>Created by: {{ $product->creator->name }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Statistics -->
        <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Usage Statistics</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">How this product is being used</p>
            </div>
            <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ $product->businesses()->count() }}</div>
                        <div class="text-sm text-gray-500">Businesses Using</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">{{ $product->created_at->diffForHumans() }}</div>
                        <div class="text-sm text-gray-500">Age</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const iconSelect = document.getElementById('icon');
    const iconPreview = document.getElementById('icon-preview');
    const iconLabel = document.getElementById('icon-label');
    const availableIcons = @json($availableIcons);

    iconSelect.addEventListener('change', function() {
        const selectedIcon = this.value;
        iconPreview.className = selectedIcon + ' text-2xl text-gray-600 mr-3';
        iconLabel.textContent = availableIcons[selectedIcon] || 'General Product';
    });
});
</script>
@endsection
