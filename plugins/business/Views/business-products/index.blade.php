@extends('layouts.app')

@section('title', 'Business Products')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Products: {{ $business->name }}</h1>
                <p class="text-gray-600 mt-1">Manage products and services for this business</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('business.show', $business) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Business
                </a>
                @if(auth()->user()->hasPermission('manage_businesses') && $availableProducts->count() > 0)
                    <a href="{{ route('business.products.create', $business) }}" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Assign Product
                    </a>
                @endif
            </div>
        </div>

        <!-- Assigned Products -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Assigned Products ({{ $assignedProducts->count() }})
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">Products currently assigned to this business</p>
            </div>
            <div class="border-t border-gray-200">
                @if($assignedProducts->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pricing</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($assignedProducts as $product)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">{{ $product->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $product->category_label }}</div>
                                                @if($product->pivot->notes)
                                                    <div class="text-xs text-gray-400 mt-1">{{ Str::limit($product->pivot->notes, 50) }}</div>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                @if($product->pivot->custom_price)
                                                    ${{ number_format($product->pivot->custom_price, 2) }}
                                                @elseif($product->base_price)
                                                    ${{ number_format($product->base_price, 2) }}
                                                @else
                                                    Custom pricing
                                                @endif
                                            </div>
                                            @if($product->pivot->pricing_model)
                                                <div class="text-sm text-gray-500">{{ ucfirst($product->pivot->pricing_model) }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                {{ $product->pivot->status === 'active' ? 'bg-green-100 text-green-800' : 
                                                   ($product->pivot->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                                   ($product->pivot->status === 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800')) }}">
                                                {{ ucfirst($product->pivot->status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            @if($product->pivot->start_date)
                                                <div>Start: {{ \Carbon\Carbon::parse($product->pivot->start_date)->format('M d, Y') }}</div>
                                            @endif
                                            @if($product->pivot->end_date)
                                                <div>End: {{ \Carbon\Carbon::parse($product->pivot->end_date)->format('M d, Y') }}</div>
                                            @endif
                                            @if(!$product->pivot->start_date && !$product->pivot->end_date)
                                                <span class="text-gray-400">No dates set</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                @if(auth()->user()->hasPermission('manage_businesses'))
                                                    <a href="{{ route('business.products.edit', [$business, $product]) }}" 
                                                       class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                                    <form method="POST" action="{{ route('business.products.remove', [$business, $product]) }}" 
                                                          onsubmit="return confirm('Remove this product from the business?')" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900">Remove</button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="px-6 py-8 text-center">
                        <p class="text-gray-500">No products assigned to this business yet.</p>
                        @if(auth()->user()->hasPermission('manage_businesses') && $availableProducts->count() > 0)
                            <a href="{{ route('business.products.create', $business) }}" 
                               class="mt-2 text-blue-600 hover:text-blue-800 text-sm">
                                Assign first product
                            </a>
                        @endif
                    </div>
                @endif
            </div>
        </div>

        <!-- Available Products -->
        @if($availableProducts->count() > 0)
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                        Available Products ({{ $availableProducts->count() }})
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Products that can be assigned to this business</p>
                </div>
                <div class="border-t border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                        @foreach($availableProducts as $product)
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">{{ $product->name }}</h4>
                                        <p class="text-xs text-gray-500">{{ $product->category_label }}</p>
                                    </div>
                                    @if(auth()->user()->hasPermission('manage_businesses'))
                                        <a href="{{ route('business.products.create', $business) }}?product={{ $product->id }}" 
                                           class="text-blue-600 hover:text-blue-800 text-sm">
                                            Assign
                                        </a>
                                    @endif
                                </div>
                                
                                @if($product->description)
                                    <p class="text-xs text-gray-600 mb-3">{{ Str::limit($product->description, 80) }}</p>
                                @endif
                                
                                <div class="text-xs text-gray-500 space-y-1">
                                    <div class="flex justify-between">
                                        <span>Base Price:</span>
                                        <span>{{ $product->formatted_base_price }}</span>
                                    </div>
                                    @if($product->pricing_model)
                                        <div class="flex justify-between">
                                            <span>Model:</span>
                                            <span>{{ $product->pricing_model_label }}</span>
                                        </div>
                                    @endif
                                </div>


                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- Product Statistics -->
        <div class="mt-8 bg-gray-50 border border-gray-200 rounded-md p-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-600">{{ $assignedProducts->count() }}</div>
                    <div class="text-sm text-gray-500">Assigned Products</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-600">{{ $assignedProducts->where('pivot.status', 'active')->count() }}</div>
                    <div class="text-sm text-gray-500">Active Products</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-yellow-600">{{ $assignedProducts->where('pivot.status', 'pending')->count() }}</div>
                    <div class="text-sm text-gray-500">Pending Products</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-purple-600">{{ $availableProducts->count() }}</div>
                    <div class="text-sm text-gray-500">Available Products</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
