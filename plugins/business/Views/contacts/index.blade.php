@extends('layouts.app')

@section('title', 'Business Contacts')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Contacts: {{ $business->name }}</h1>
                <p class="text-gray-600 mt-1">Manage contacts for this business</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('business.show', $business) }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Business
                </a>
                @if(auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('business.contacts.create', $business) }}" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Add Contact
                    </a>
                @endif
            </div>
        </div>

        <!-- Contacts List -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            @if($contacts->count() > 0)
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Info</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($contacts as $contact)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                <i class="fas fa-user text-gray-600"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $contact->name }}
                                                @if($contact->is_primary)
                                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        Primary
                                                    </span>
                                                @endif
                                            </div>
                                            @if($contact->department)
                                                <div class="text-sm text-gray-500">{{ $contact->department }}</div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $contact->position ?? 'N/A' }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        @if($contact->email)
                                            <div class="flex items-center">
                                                <i class="fas fa-envelope text-gray-400 mr-2"></i>
                                                <a href="mailto:{{ $contact->email }}" class="text-blue-600 hover:text-blue-800">
                                                    {{ $contact->email }}
                                                </a>
                                            </div>
                                        @endif
                                        @if($contact->phone)
                                            <div class="flex items-center mt-1">
                                                <i class="fas fa-phone text-gray-400 mr-2"></i>
                                                <a href="tel:{{ $contact->phone }}" class="text-blue-600 hover:text-blue-800">
                                                    {{ $contact->formatted_phone }}
                                                </a>
                                            </div>
                                        @endif
                                        @if(!$contact->email && !$contact->phone)
                                            <span class="text-gray-500">No contact info</span>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $contact->isComplete() ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $contact->isComplete() ? 'Complete' : 'Incomplete' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="{{ route('business.contacts.show', [$business, $contact]) }}" 
                                           class="text-blue-600 hover:text-blue-900">View</a>
                                        @if(auth()->user()->hasPermission('manage_businesses'))
                                            <a href="{{ route('business.contacts.edit', [$business, $contact]) }}" 
                                               class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                            @if(!$contact->is_primary)
                                                <form method="POST" action="{{ route('business.contacts.set-primary', [$business, $contact]) }}" class="inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button type="submit" class="text-green-600 hover:text-green-900">
                                                        Set Primary
                                                    </button>
                                                </form>
                                            @endif
                                            <form method="POST" action="{{ route('business.contacts.destroy', [$business, $contact]) }}" 
                                                  onsubmit="return confirm('Are you sure you want to delete this contact?')" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="text-center py-12">
                    <div class="text-gray-500 text-lg">No contacts found for this business.</div>
                    @if(auth()->user()->hasPermission('manage_businesses'))
                        <a href="{{ route('business.contacts.create', $business) }}" 
                           class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Add First Contact
                        </a>
                    @endif
                </div>
            @endif
        </div>

        <!-- Quick Stats -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-users text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Contacts</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $contacts->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-envelope text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">With Email</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $contacts->whereNotNull('email')->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-phone text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">With Phone</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $contacts->whereNotNull('phone')->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
