@extends('layouts.app')

@section('title', 'All Contacts')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">All Contacts</h1>
                <p class="text-gray-600 mt-1">Manage contacts across all businesses</p>
            </div>
        </div>

        <!-- Search and Filter Form -->
        <div class="bg-white shadow rounded-lg p-6 mb-6">
            <form method="GET" action="{{ route('contacts.index') }}" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-64">
                    <input type="text" name="search" value="{{ request('search') }}" 
                           placeholder="Search contacts..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <select name="business_id" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Businesses</option>
                        @foreach($businesses as $business)
                            <option value="{{ $business->id }}" {{ request('business_id') == $business->id ? 'selected' : '' }}>
                                {{ $business->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <select name="is_primary" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Contacts</option>
                        <option value="1" {{ request('is_primary') === '1' ? 'selected' : '' }}>Primary Only</option>
                        <option value="0" {{ request('is_primary') === '0' ? 'selected' : '' }}>Non-Primary Only</option>
                    </select>
                </div>
                <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>
                @if(request()->hasAny(['search', 'business_id', 'is_primary']))
                    <a href="{{ route('contacts.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Clear
                    </a>
                @endif
            </form>
        </div>

        <!-- Contacts Table -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            @if($contacts->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Info</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($contacts as $contact)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="h-10 w-10 flex-shrink-0">
                                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <i class="fas fa-user text-gray-600"></i>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">{{ $contact->name }}</div>
                                                @if($contact->department)
                                                    <div class="text-sm text-gray-500">{{ $contact->department }}</div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img class="h-8 w-8 rounded-full mr-3" src="{{ $contact->business->logo_url }}" alt="{{ $contact->business->name }}">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">{{ $contact->business->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $contact->business->status_label }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $contact->position ?: 'N/A' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $contact->email ?: 'N/A' }}</div>
                                        <div class="text-sm text-gray-500">{{ $contact->phone ?: 'N/A' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($contact->is_primary)
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Primary
                                            </span>
                                        @else
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                Contact
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="{{ route('business.show', $contact->business) }}" 
                                               class="text-blue-600 hover:text-blue-900" title="View Business">
                                                <i class="fas fa-building"></i>
                                            </a>
                                            @if($contact->email)
                                                <a href="mailto:{{ $contact->email }}" 
                                                   class="text-green-600 hover:text-green-900" title="Send Email">
                                                    <i class="fas fa-envelope"></i>
                                                </a>
                                            @endif
                                            @if($contact->phone)
                                                <a href="tel:{{ $contact->phone }}" 
                                                   class="text-purple-600 hover:text-purple-900" title="Call">
                                                    <i class="fas fa-phone"></i>
                                                </a>
                                            @endif
                                            @if(auth()->user()->hasPermission('manage_businesses'))
                                                <a href="{{ route('business.contacts.edit', [$contact->business, $contact]) }}" 
                                                   class="text-indigo-600 hover:text-indigo-900" title="Edit Contact">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $contacts->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-address-book text-gray-400 text-4xl mb-4"></i>
                    <div class="text-gray-500 text-lg">No contacts found.</div>
                    <p class="text-gray-400 text-sm mt-2">
                        @if(request()->hasAny(['search', 'business_id', 'is_primary']))
                            Try adjusting your search criteria.
                        @else
                            Contacts will appear here when businesses add them.
                        @endif
                    </p>
                </div>
            @endif
        </div>

        <!-- Contact Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-address-book text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Contacts</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $contacts->total() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-star text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Primary Contacts</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $contacts->where('is_primary', true)->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-building text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Businesses</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $contacts->pluck('business_id')->unique()->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-envelope text-orange-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">With Email</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $contacts->whereNotNull('email')->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
