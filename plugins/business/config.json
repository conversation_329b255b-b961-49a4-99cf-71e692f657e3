{"name": "business", "version": "1.0.0", "enabled": true, "dependencies": ["users"], "description": "Business management functionality with role-based access control", "system": false, "category": "Business Management", "permissions": ["manage_businesses", "view_businesses", "manage_business_users", "view_business_reports"], "navigation": {"label": "Business", "icon": "fas fa-building", "route": "", "permissions": ["view_businesses"], "subnav": [{"label": "New Business", "icon": "fas fa-plus", "route": "business.create", "permissions": ["manage_businesses"]}, {"label": "All Businesses", "icon": "fas fa-building", "route": "business.index", "permissions": ["view_businesses"]}, {"label": "Contacts", "icon": "fas fa-address-book", "route": "contacts.index", "permissions": ["view_businesses"]}]}}