/**
 * Chat Interface Validation Script
 * This script validates that the chat interface is working properly
 */

// Validation functions
function validateDOMElements() {
    const requiredElements = [
        'activity-content',
        'messages-container',
        'messages-list',
        'messages-loading',
        'messages-empty',
        'message-form',
        'message-input',
        'message-type-selector',
        'send-btn'
    ];
    
    const results = {
        passed: 0,
        failed: 0,
        missing: []
    };
    
    console.log('🔍 Validating DOM Elements...');
    
    requiredElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            results.passed++;
            console.log(`✅ Found: ${id}`);
        } else {
            results.failed++;
            results.missing.push(id);
            console.log(`❌ Missing: ${id}`);
        }
    });
    
    return results;
}

function validateChatManager() {
    console.log('🔍 Validating ChatManager...');
    
    if (typeof ChatManager === 'undefined') {
        console.log('❌ ChatManager class not defined');
        return false;
    }
    
    console.log('✅ ChatManager class defined');
    
    // Test instantiation
    try {
        const testManager = new ChatManager();
        console.log('✅ ChatManager can be instantiated');
        
        // Check required methods
        const requiredMethods = [
            'init',
            'loadMessages',
            'sendMessage',
            'setupEventListeners',
            'autoResizeTextarea',
            'scrollToBottom'
        ];
        
        let methodsFound = 0;
        requiredMethods.forEach(method => {
            if (typeof testManager[method] === 'function') {
                methodsFound++;
                console.log(`✅ Method found: ${method}`);
            } else {
                console.log(`❌ Method missing: ${method}`);
            }
        });
        
        return methodsFound === requiredMethods.length;
    } catch (error) {
        console.log('❌ ChatManager instantiation failed:', error.message);
        return false;
    }
}

function validateEventListeners() {
    console.log('🔍 Validating Event Listeners...');
    
    const messageForm = document.getElementById('message-form');
    const messageInput = document.getElementById('message-input');
    const sendBtn = document.getElementById('send-btn');
    
    let validationsPassed = 0;
    
    // Test form submission
    if (messageForm) {
        try {
            const testEvent = new Event('submit');
            messageForm.dispatchEvent(testEvent);
            console.log('✅ Form submission event works');
            validationsPassed++;
        } catch (error) {
            console.log('❌ Form submission event failed:', error.message);
        }
    }
    
    // Test input events
    if (messageInput) {
        try {
            const testEvent = new Event('input');
            messageInput.dispatchEvent(testEvent);
            console.log('✅ Input event works');
            validationsPassed++;
        } catch (error) {
            console.log('❌ Input event failed:', error.message);
        }
    }
    
    // Test button click
    if (sendBtn) {
        try {
            const testEvent = new Event('click');
            sendBtn.dispatchEvent(testEvent);
            console.log('✅ Button click event works');
            validationsPassed++;
        } catch (error) {
            console.log('❌ Button click event failed:', error.message);
        }
    }
    
    return validationsPassed >= 2;
}

function validateLayout() {
    console.log('🔍 Validating Layout...');
    
    const chatContainer = document.querySelector('[style*="height: 600px"]');
    const messagesContainer = document.getElementById('messages-container');
    const messageInput = document.getElementById('message-input');
    
    let layoutValidations = 0;
    
    if (chatContainer) {
        console.log('✅ Chat container has proper height');
        layoutValidations++;
    } else {
        console.log('❌ Chat container height not set properly');
    }
    
    if (messagesContainer) {
        const styles = window.getComputedStyle(messagesContainer);
        if (styles.overflowY === 'auto') {
            console.log('✅ Messages container has proper scrolling');
            layoutValidations++;
        } else {
            console.log('❌ Messages container scrolling not configured');
        }
    }
    
    if (messageInput) {
        const styles = window.getComputedStyle(messageInput);
        if (styles.minHeight) {
            console.log('✅ Message input has proper sizing');
            layoutValidations++;
        } else {
            console.log('❌ Message input sizing not configured');
        }
    }
    
    return layoutValidations >= 2;
}

// Main validation function
function runChatValidation() {
    console.log('🚀 Starting Chat Interface Validation...');
    console.log('=====================================');
    
    const results = {
        domElements: validateDOMElements(),
        chatManager: validateChatManager(),
        eventListeners: validateEventListeners(),
        layout: validateLayout()
    };
    
    console.log('=====================================');
    console.log('📊 Validation Results:');
    console.log(`DOM Elements: ${results.domElements.passed}/${results.domElements.passed + results.domElements.failed} passed`);
    console.log(`ChatManager: ${results.chatManager ? 'PASS' : 'FAIL'}`);
    console.log(`Event Listeners: ${results.eventListeners ? 'PASS' : 'FAIL'}`);
    console.log(`Layout: ${results.layout ? 'PASS' : 'FAIL'}`);
    
    const overallPass = results.domElements.failed === 0 && 
                       results.chatManager && 
                       results.eventListeners && 
                       results.layout;
    
    console.log('=====================================');
    console.log(`🎯 Overall Result: ${overallPass ? '✅ PASS' : '❌ FAIL'}`);
    
    if (!overallPass) {
        console.log('🔧 Issues to fix:');
        if (results.domElements.failed > 0) {
            console.log(`- Missing DOM elements: ${results.domElements.missing.join(', ')}`);
        }
        if (!results.chatManager) {
            console.log('- ChatManager class issues');
        }
        if (!results.eventListeners) {
            console.log('- Event listener problems');
        }
        if (!results.layout) {
            console.log('- Layout configuration issues');
        }
    }
    
    return overallPass;
}

// Auto-run validation when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runChatValidation);
} else {
    runChatValidation();
}
