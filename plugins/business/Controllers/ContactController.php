<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Business\Models\Business;
use Plugins\Business\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class ContactController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);
            
            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_businesses',
            'show' => 'view_businesses',
            'create' => 'manage_businesses',
            'store' => 'manage_businesses',
            'edit' => 'manage_businesses',
            'update' => 'manage_businesses',
            'destroy' => 'manage_businesses',
            'setPrimary' => 'manage_businesses',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Display contacts for a business
     */
    public function index(Business $business): View
    {
        $contacts = $business->contacts()->latest()->get();
        return view('plugins.business::contacts.index', compact('business', 'contacts'));
    }

    /**
     * Show the form for creating a new contact
     */
    public function create(Business $business): View
    {
        return view('plugins.business::contacts.create', compact('business'));
    }

    /**
     * Store a newly created contact
     */
    public function store(Request $request, Business $business): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'is_primary' => 'boolean',
            'notes' => 'nullable|string',
        ]);

        $contact = $business->contacts()->create([
            'name' => $request->name,
            'position' => $request->position,
            'department' => $request->department,
            'email' => $request->email,
            'phone' => $request->phone,
            'is_primary' => $request->boolean('is_primary', false),
            'notes' => $request->notes,
        ]);

        // If this is set as primary, ensure it's the only primary contact
        if ($contact->is_primary) {
            $contact->setAsPrimary();
        }

        return redirect()->route('business.contacts.index', $business)
                        ->with('success', 'Contact created successfully.');
    }

    /**
     * Display the specified contact
     */
    public function show(Business $business, Contact $contact): View
    {
        // Ensure contact belongs to business
        if ($contact->business_id !== $business->id) {
            abort(404);
        }

        return view('plugins.business::contacts.show', compact('business', 'contact'));
    }

    /**
     * Show the form for editing the specified contact
     */
    public function edit(Business $business, Contact $contact): View
    {
        // Ensure contact belongs to business
        if ($contact->business_id !== $business->id) {
            abort(404);
        }

        return view('plugins.business::contacts.edit', compact('business', 'contact'));
    }

    /**
     * Update the specified contact
     */
    public function update(Request $request, Business $business, Contact $contact): RedirectResponse
    {
        // Ensure contact belongs to business
        if ($contact->business_id !== $business->id) {
            abort(404);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'is_primary' => 'boolean',
            'notes' => 'nullable|string',
        ]);

        $contact->update([
            'name' => $request->name,
            'position' => $request->position,
            'department' => $request->department,
            'email' => $request->email,
            'phone' => $request->phone,
            'is_primary' => $request->boolean('is_primary', false),
            'notes' => $request->notes,
        ]);

        // If this is set as primary, ensure it's the only primary contact
        if ($contact->is_primary) {
            $contact->setAsPrimary();
        }

        return redirect()->route('business.contacts.index', $business)
                        ->with('success', 'Contact updated successfully.');
    }

    /**
     * Remove the specified contact
     */
    public function destroy(Business $business, Contact $contact): RedirectResponse
    {
        // Ensure contact belongs to business
        if ($contact->business_id !== $business->id) {
            abort(404);
        }

        $contact->delete();

        return redirect()->route('business.contacts.index', $business)
                        ->with('success', 'Contact deleted successfully.');
    }

    /**
     * Set contact as primary
     */
    public function setPrimary(Business $business, Contact $contact): RedirectResponse
    {
        // Ensure contact belongs to business
        if ($contact->business_id !== $business->id) {
            abort(404);
        }

        $contact->setAsPrimary();

        return redirect()->back()
                        ->with('success', 'Contact set as primary successfully.');
    }

    /**
     * Display a global listing of all contacts
     */
    public function globalIndex(Request $request): View
    {
        $query = Contact::with(['business'])
                        ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('position', 'like', "%{$search}%")
                  ->orWhere('department', 'like', "%{$search}%");
            });
        }

        // Filter by business
        if ($request->filled('business_id')) {
            $query->where('business_id', $request->business_id);
        }

        // Filter by primary contacts
        if ($request->filled('is_primary')) {
            $query->where('is_primary', $request->is_primary === '1');
        }

        $contacts = $query->paginate(20)->withQueryString();

        // Get businesses for filter dropdown
        $businesses = Business::orderBy('name')->get(['id', 'name']);

        return view('plugins.business::contacts.global-index', compact('contacts', 'businesses'));
    }
}
