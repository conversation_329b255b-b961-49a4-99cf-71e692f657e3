<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Plugins\Business\Models\Business;
use Plugins\Business\Models\BusinessNotificationPreference;

class BusinessNotificationController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            
            if (!$user->hasPermission('view_businesses')) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Show notification preferences for a business
     */
    public function show(Business $business): JsonResponse
    {
        $preferences = BusinessNotificationPreference::getForUserAndBusiness(
            auth()->user(),
            $business
        );

        return response()->json([
            'success' => true,
            'preferences' => $preferences,
            'activity_types' => $this->getActivityTypeOptions(),
            'frequency_options' => BusinessNotificationPreference::getFrequencyOptions(),
            'severity_options' => BusinessNotificationPreference::getSeverityOptions(),
        ]);
    }

    /**
     * Update notification preferences for a business
     */
    public function update(Request $request, Business $business): JsonResponse
    {
        $request->validate([
            'email_enabled' => 'boolean',
            'sms_enabled' => 'boolean',
            'whatsapp_enabled' => 'boolean',
            'in_app_enabled' => 'boolean',
            'frequency' => 'required|string|in:immediate,hourly,daily,weekly,never',
            'activity_types' => 'array',
            'activity_types.*' => 'string',
            'min_severity' => 'required|string|in:info,warning,important,critical',
            'notification_email' => 'nullable|email',
            'notification_phone' => 'nullable|string',
            'notification_whatsapp' => 'nullable|string',
            'quiet_hours_start' => 'nullable|date_format:H:i',
            'quiet_hours_end' => 'nullable|date_format:H:i',
            'quiet_days' => 'array',
            'quiet_days.*' => 'integer|min:0|max:6',
        ]);

        $preferences = BusinessNotificationPreference::getForUserAndBusiness(
            auth()->user(),
            $business
        );

        $preferences->update([
            'email_enabled' => $request->boolean('email_enabled'),
            'sms_enabled' => $request->boolean('sms_enabled'),
            'whatsapp_enabled' => $request->boolean('whatsapp_enabled'),
            'in_app_enabled' => $request->boolean('in_app_enabled'),
            'frequency' => $request->frequency,
            'activity_types' => $request->activity_types ?? [],
            'min_severity' => $request->min_severity,
            'notification_email' => $request->notification_email,
            'notification_phone' => $request->notification_phone,
            'notification_whatsapp' => $request->notification_whatsapp,
            'quiet_hours_start' => $request->quiet_hours_start,
            'quiet_hours_end' => $request->quiet_hours_end,
            'quiet_days' => $request->quiet_days ?? [],
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences updated successfully.',
            'preferences' => $preferences
        ]);
    }

    /**
     * Reset notification preferences to defaults
     */
    public function reset(Business $business): JsonResponse
    {
        $preferences = BusinessNotificationPreference::getForUserAndBusiness(
            auth()->user(),
            $business
        );

        $defaults = BusinessNotificationPreference::getDefaults();
        $defaults['notification_email'] = auth()->user()->email;

        $preferences->update($defaults);

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences reset to defaults.',
            'preferences' => $preferences
        ]);
    }

    /**
     * Test notification delivery
     */
    public function test(Request $request, Business $business): JsonResponse
    {
        $request->validate([
            'channel' => 'required|string|in:email,sms,whatsapp,in_app',
        ]);

        $preferences = BusinessNotificationPreference::getForUserAndBusiness(
            auth()->user(),
            $business
        );

        $channel = $request->channel;
        $enabledChannels = $preferences->getEnabledChannels();

        if (!isset($enabledChannels[$channel])) {
            return response()->json([
                'success' => false,
                'message' => "The {$channel} channel is not enabled or configured."
            ], 400);
        }

        try {
            // Send test notification based on channel
            $result = $this->sendTestNotification($channel, $enabledChannels[$channel], $business);

            return response()->json([
                'success' => true,
                'message' => "Test notification sent successfully via {$channel}.",
                'details' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => "Failed to send test notification: " . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get activity type options for preferences
     */
    protected function getActivityTypeOptions(): array
    {
        $activityTypes = [
            'comment' => 'Comments',
            'document_upload' => 'Document Uploads',
            'document_delete' => 'Document Deletions',
            'status_change' => 'Status Changes',
            'tag_assignment' => 'Tag Assignments',
            'tag_removal' => 'Tag Removals',
            'product_assignment' => 'Product Assignments',
            'product_removal' => 'Product Removals',
            'user_assignment' => 'User Assignments',
            'user_removal' => 'User Removals',
            'business_update' => 'Business Updates',
            'system_log' => 'System Logs',
            'manual_log' => 'Manual Logs',
            'file_share' => 'File Sharing',
            'mention' => 'Mentions',
        ];

        return $activityTypes;
    }

    /**
     * Send test notification
     */
    protected function sendTestNotification(string $channel, $destination, Business $business): array
    {
        $message = "This is a test notification for {$business->name}. Your notification preferences are working correctly.";

        switch ($channel) {
            case 'email':
                // In a real implementation, you would send an actual email
                // For now, we'll just simulate it
                return [
                    'channel' => 'email',
                    'destination' => $destination,
                    'message' => $message,
                    'status' => 'simulated',
                    'timestamp' => now()->toISOString()
                ];

            case 'sms':
                // In a real implementation, you would use an SMS service like Twilio
                return [
                    'channel' => 'sms',
                    'destination' => $destination,
                    'message' => $message,
                    'status' => 'simulated',
                    'timestamp' => now()->toISOString()
                ];

            case 'whatsapp':
                // In a real implementation, you would use WhatsApp Business API
                return [
                    'channel' => 'whatsapp',
                    'destination' => $destination,
                    'message' => $message,
                    'status' => 'simulated',
                    'timestamp' => now()->toISOString()
                ];

            case 'in_app':
                // For in-app notifications, you might store in a notifications table
                return [
                    'channel' => 'in_app',
                    'message' => $message,
                    'status' => 'delivered',
                    'timestamp' => now()->toISOString()
                ];

            default:
                throw new \InvalidArgumentException("Unsupported notification channel: {$channel}");
        }
    }

    /**
     * Get notification history for a business
     */
    public function history(Request $request, Business $business): JsonResponse
    {
        // In a real implementation, you would have a notifications table
        // to track sent notifications. For now, we'll return a placeholder.
        
        return response()->json([
            'success' => true,
            'notifications' => [],
            'message' => 'Notification history feature will be implemented with a notifications table.'
        ]);
    }

    /**
     * Mark notifications as read
     */
    public function markAsRead(Request $request, Business $business): JsonResponse
    {
        $request->validate([
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'integer',
        ]);

        // In a real implementation, you would update the notifications table
        // to mark the specified notifications as read.

        return response()->json([
            'success' => true,
            'message' => 'Notifications marked as read.',
            'marked_count' => count($request->notification_ids)
        ]);
    }
}
