<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Business\Models\Business;
use Plugins\Business\Models\Document;
use Plugins\Business\Models\BusinessActivity;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DocumentController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);
            
            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_businesses',
            'show' => 'view_businesses',
            'create' => 'manage_businesses',
            'store' => 'manage_businesses',
            'destroy' => 'manage_businesses',
            'download' => 'view_businesses',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Display documents for a business
     */
    public function index(Business $business): View
    {
        $documents = $business->documents()->with('uploader')->latest()->get();
        $documentTypes = Document::getDocumentTypes();
        
        return view('plugins.business::documents.index', compact('business', 'documents', 'documentTypes'));
    }

    /**
     * Show the form for uploading a new document
     */
    public function create(Business $business): View
    {
        $documentTypes = Document::getDocumentTypes();
        return view('plugins.business::documents.create', compact('business', 'documentTypes'));
    }

    /**
     * Store a newly uploaded document
     */
    public function store(Request $request, Business $business): RedirectResponse
    {
        $request->validate([
            'document_type' => 'required|in:' . implode(',', array_keys(Document::getDocumentTypes())),
            'file' => 'required|file|max:10240|mimes:pdf,doc,docx,jpg,jpeg,png,gif,txt,xlsx,xls,ppt,pptx',
            'description' => 'nullable|string|max:500',
        ]);

        $file = $request->file('file');
        $originalName = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        
        // Generate unique filename
        $fileName = Str::uuid() . '.' . $extension;
        
        // Store file in business-specific directory
        $filePath = "business-documents/{$business->id}/" . $fileName;
        $file->storeAs('', $filePath, 'local');

        // Create document record
        $document = $business->documents()->create([
            'document_type' => $request->document_type,
            'file_name' => $fileName,
            'original_name' => $originalName,
            'file_path' => $filePath,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'description' => $request->description,
            'uploaded_by' => Auth::id(),
            'upload_date' => now(),
        ]);

        // Log document upload activity
        BusinessActivity::logUserAction(
            $business,
            'document_upload',
            'Document uploaded',
            "Document '{$originalName}' was uploaded",
            [
                'document_id' => $document->id,
                'document_type' => $request->document_type,
                'file_name' => $originalName,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
            ]
        );

        return redirect()->route('business.documents.index', $business)
                        ->with('success', 'Document uploaded successfully.');
    }

    /**
     * Remove the specified document
     */
    public function destroy(Business $business, Document $document): RedirectResponse
    {
        // Ensure document belongs to business
        if ($document->business_id !== $business->id) {
            abort(404);
        }

        // Store document info for logging before deletion
        $documentInfo = [
            'document_id' => $document->id,
            'document_type' => $document->document_type,
            'file_name' => $document->original_name,
            'file_size' => $document->file_size,
        ];

        // Delete file from storage
        if ($document->fileExists()) {
            Storage::disk('local')->delete($document->file_path);
        }

        // Delete document record
        $document->delete();

        // Log document deletion activity
        BusinessActivity::logUserAction(
            $business,
            'document_delete',
            'Document deleted',
            "Document '{$documentInfo['file_name']}' was deleted",
            $documentInfo
        );

        return redirect()->route('business.documents.index', $business)
                        ->with('success', 'Document deleted successfully.');
    }

    /**
     * Download the specified document
     */
    public function download(Business $business, Document $document): Response
    {
        // Ensure document belongs to business
        if ($document->business_id !== $business->id) {
            abort(404);
        }

        // Check if file exists
        if (!$document->fileExists()) {
            abort(404, 'File not found.');
        }

        return Storage::disk('local')->download($document->file_path, $document->original_name);
    }

    /**
     * View document (for images and PDFs)
     */
    public function view(Business $business, Document $document): Response
    {
        // Ensure document belongs to business
        if ($document->business_id !== $business->id) {
            abort(404);
        }

        // Check if file exists
        if (!$document->fileExists()) {
            abort(404, 'File not found.');
        }

        // Only allow viewing of images and PDFs
        if (!$document->isImage() && !$document->isPdf()) {
            return $this->download($business, $document);
        }

        $fileContent = Storage::disk('local')->get($document->file_path);
        
        return response($fileContent)
            ->header('Content-Type', $document->mime_type)
            ->header('Content-Disposition', 'inline; filename="' . $document->original_name . '"');
    }

    /**
     * Display a global listing of all documents
     */
    public function globalIndex(Request $request): View
    {
        $query = Document::with(['business'])
                         ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('original_name', 'like', "%{$search}%")
                  ->orWhere('document_type', 'like', "%{$search}%");
            });
        }

        // Filter by business
        if ($request->filled('business_id')) {
            $query->where('business_id', $request->business_id);
        }

        // Filter by document type
        if ($request->filled('document_type')) {
            $query->where('document_type', $request->document_type);
        }

        $documents = $query->paginate(20)->withQueryString();

        // Get businesses for filter dropdown
        $businesses = Business::orderBy('name')->get(['id', 'name']);

        // Get document types for filter
        $documentTypes = Document::getDocumentTypes();

        return view('plugins.business::documents.global-index', compact('documents', 'businesses', 'documentTypes'));
    }
}
