<?php

namespace Plugins\users\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AuthController extends Controller
{
    /**
     * Show the login form
     */
    public function showLoginForm(): View
    {
        return view('plugins.users.login');
    }

    /**
     * Handle login attempt
     */
    public function login(Request $request): RedirectResponse
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();

            return redirect()->intended('/');
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    /**
     * Handle logout
     */
    public function logout(Request $request): RedirectResponse
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }

    /**
     * Show unauthorized page
     */
    public function unauthorized(): View
    {
        return view('plugins.users.unauthorized');
    }

    /**
     * Show user profile (if authenticated)
     */
    public function profile(): View
    {
        if (!Auth::check()) {
            return redirect()->route('users.login');
        }

        return view('plugins.users.profile', [
            'user' => Auth::user()
        ]);
    }

    /**
     * Create a default admin user (for development)
     */
    public function createAdmin(): RedirectResponse
    {
        // Check if admin already exists
        if (User::where('email', '<EMAIL>')->exists()) {
            return redirect()->back()->with('error', 'Admin user already exists.');
        }

        User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        return redirect()->back()->with('success', 'Admin user created successfully. Email: <EMAIL>, Password: password');
    }
}
