@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Role: {{ $role->display_name }}</h1>
            <p class="mt-1 text-sm text-gray-600">Modify role details and permissions</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('roles.show', $role) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-eye mr-2"></i>
                View Role
            </a>
            <a href="{{ route('roles.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Roles
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="POST" action="{{ route('roles.update', $role) }}" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Basic Information -->
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                        <p class="mt-1 text-sm text-gray-600">Update basic details about the role.</p>
                    </div>

                    <!-- Display Name -->
                    <div>
                        <label for="display_name" class="block text-sm font-medium text-gray-700">
                            Display Name <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1">
                            <input type="text" name="display_name" id="display_name" value="{{ old('display_name', $role->display_name) }}" required
                                   class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md @error('display_name') border-red-300 @enderror">
                        </div>
                        @error('display_name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- System Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            System Name <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1">
                            <input type="text" name="name" id="name" value="{{ old('name', $role->name) }}" required
                                   class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md @error('name') border-red-300 @enderror"
                                   {{ in_array($role->name, ['admin', 'user']) ? 'readonly' : '' }}>
                        </div>
                        @if(in_array($role->name, ['admin', 'user']))
                            <p class="mt-2 text-sm text-yellow-600">System role names cannot be changed.</p>
                        @else
                            <p class="mt-2 text-sm text-gray-500">Lowercase letters, numbers, and underscores only. Used internally by the system.</p>
                        @endif
                        @error('name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">
                            Description
                        </label>
                        <div class="mt-1">
                            <textarea name="description" id="description" rows="3"
                                      class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md @error('description') border-red-300 @enderror"
                                      placeholder="Describe what this role is responsible for...">{{ old('description', $role->description) }}</textarea>
                        </div>
                        @error('description')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Permissions -->
                <div class="pt-6 border-t border-gray-200">
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Permissions</h3>
                            <p class="mt-1 text-sm text-gray-600">Select the permissions this role should have.</p>
                        </div>

                        @if($permissions->count() > 0)
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="select_all" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                    <label for="select_all" class="ml-2 text-sm font-medium text-gray-700">Select All Permissions</label>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    @foreach($permissions as $permission)
                                        @php
                                            $isChecked = in_array($permission->id, old('permissions', $role->permissions->pluck('id')->toArray()));
                                        @endphp
                                        <div class="relative flex items-start p-3 border border-gray-200 rounded-lg hover:bg-gray-50 {{ $isChecked ? 'bg-primary-50 border-primary-200' : '' }}">
                                            <div class="flex items-center h-5">
                                                <input id="permission_{{ $permission->id }}" name="permissions[]" type="checkbox" value="{{ $permission->id }}"
                                                       {{ $isChecked ? 'checked' : '' }}
                                                       class="permission-checkbox focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                            </div>
                                            <div class="ml-3 text-sm">
                                                <label for="permission_{{ $permission->id }}" class="font-medium text-gray-700 cursor-pointer">
                                                    {{ $permission->display_name }}
                                                </label>
                                                @if($permission->description)
                                                    <p class="text-gray-500">{{ $permission->description }}</p>
                                                @endif
                                                <p class="text-xs text-gray-400">{{ $permission->name }}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-key text-4xl text-gray-300 mb-4"></i>
                                <p class="text-sm text-gray-500">No permissions available to assign.</p>
                                <a href="{{ route('permissions.create') }}" class="mt-2 inline-flex items-center text-sm text-primary-600 hover:text-primary-500">
                                    <i class="fas fa-plus mr-1"></i>
                                    Create Permission
                                </a>
                            </div>
                        @endif

                        @error('permissions')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{{ route('roles.show', $role) }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-save mr-2"></i>
                        Update Role
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Current Users -->
    @if($role->users->count() > 0)
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Users with this Role</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">{{ $role->users->count() }} {{ Str::plural('user', $role->users->count()) }} currently assigned to this role.</p>
            </div>
            <div class="border-t border-gray-200">
                <div class="px-4 py-5 sm:px-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($role->users as $user)
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-user-circle text-2xl text-blue-500"></i>
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-blue-900">{{ $user->name }}</p>
                                    <p class="text-xs text-blue-700">{{ $user->email }}</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($user->is_active)
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                            Inactive
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select_all');
    const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');
    
    // Set initial state of select all checkbox
    function updateSelectAllState() {
        const checkedCount = document.querySelectorAll('.permission-checkbox:checked').length;
        const totalCount = permissionCheckboxes.length;
        
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedCount === totalCount;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
        }
    }
    
    // Initialize select all state
    updateSelectAllState();
    
    // Handle select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            permissionCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                // Update visual state
                const container = checkbox.closest('.relative');
                if (this.checked) {
                    container.classList.add('bg-primary-50', 'border-primary-200');
                } else {
                    container.classList.remove('bg-primary-50', 'border-primary-200');
                }
            });
        });
    }
    
    // Update select all checkbox when individual checkboxes change
    permissionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            
            // Update visual state
            const container = this.closest('.relative');
            if (this.checked) {
                container.classList.add('bg-primary-50', 'border-primary-200');
            } else {
                container.classList.remove('bg-primary-50', 'border-primary-200');
            }
        });
    });
});
</script>
@endsection
