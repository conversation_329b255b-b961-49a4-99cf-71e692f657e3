@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $role->display_name }}</h1>
            <p class="mt-1 text-sm text-gray-600">Role details and assigned permissions</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('roles.edit', $role) }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-edit mr-2"></i>
                Edit Role
            </a>
            <a href="{{ route('roles.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Roles
            </a>
        </div>
    </div>

    <!-- Role Information -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Role Information</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Basic details about this role.</p>
        </div>
        <div class="border-t border-gray-200">
            <dl>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Display Name</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $role->display_name }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">System Name</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $role->name }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Description</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $role->description ?: 'No description provided' }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $role->created_at->format('F j, Y \a\t g:i A') }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $role->updated_at->format('F j, Y \a\t g:i A') }}</dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- Assigned Permissions -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Assigned Permissions</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Permissions granted to this role.</p>
        </div>
        <div class="border-t border-gray-200">
            @if($role->permissions->count() > 0)
                <div class="px-4 py-5 sm:px-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($role->permissions as $permission)
                            <div class="flex items-center p-3 bg-purple-50 rounded-lg border border-purple-200">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-key text-purple-500"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-purple-900">{{ $permission->display_name }}</p>
                                    <p class="text-xs text-purple-700">{{ $permission->name }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="px-4 py-5 sm:px-6">
                    <div class="text-center py-8">
                        <i class="fas fa-key text-4xl text-gray-300 mb-4"></i>
                        <p class="text-sm text-gray-500">No permissions assigned to this role.</p>
                        <a href="{{ route('roles.edit', $role) }}" class="mt-2 inline-flex items-center text-sm text-primary-600 hover:text-primary-500">
                            <i class="fas fa-plus mr-1"></i>
                            Assign Permissions
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Assigned Users -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Users with this Role</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Users currently assigned to this role.</p>
        </div>
        <div class="border-t border-gray-200">
            @if($role->users->count() > 0)
                <div class="px-4 py-5 sm:px-6">
                    <div class="space-y-3">
                        @foreach($role->users as $user)
                            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-user-circle text-2xl text-blue-500"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-blue-900">{{ $user->name }}</p>
                                        <p class="text-xs text-blue-700">{{ $user->email }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($user->is_active)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Inactive
                                        </span>
                                    @endif
                                    <a href="{{ route('users.show', $user) }}" class="text-blue-600 hover:text-blue-900" title="View User">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="px-4 py-5 sm:px-6">
                    <div class="text-center py-8">
                        <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                        <p class="text-sm text-gray-500">No users assigned to this role.</p>
                        <a href="{{ route('users.create') }}" class="mt-2 inline-flex items-center text-sm text-primary-600 hover:text-primary-500">
                            <i class="fas fa-plus mr-1"></i>
                            Create User
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Danger Zone -->
    @if(!in_array($role->name, ['admin', 'user']) && $role->users->count() == 0)
        <div class="bg-white shadow overflow-hidden sm:rounded-lg border-l-4 border-red-400">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-red-900">Danger Zone</h3>
                <p class="mt-1 max-w-2xl text-sm text-red-600">Irreversible and destructive actions.</p>
            </div>
            <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-sm font-medium text-red-900">Delete this role</h4>
                        <p class="text-sm text-red-600">Once you delete a role, there is no going back. Please be certain.</p>
                    </div>
                    <form method="POST" action="{{ route('roles.destroy', $role) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this role? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Role
                        </button>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection
