@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Permission: {{ $permission->display_name }}</h1>
            <p class="mt-1 text-sm text-gray-600">Modify permission details</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('permissions.show', $permission) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-eye mr-2"></i>
                View Permission
            </a>
            <a href="{{ route('permissions.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Permissions
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="POST" action="{{ route('permissions.update', $permission) }}" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Basic Information -->
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                        <p class="mt-1 text-sm text-gray-600">Update basic details about the permission.</p>
                    </div>

                    <!-- Display Name -->
                    <div>
                        <label for="display_name" class="block text-sm font-medium text-gray-700">
                            Display Name <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1">
                            <input type="text" name="display_name" id="display_name" value="{{ old('display_name', $permission->display_name) }}" required
                                   class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md @error('display_name') border-red-300 @enderror">
                        </div>
                        @error('display_name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- System Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            System Name <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1">
                            <input type="text" name="name" id="name" value="{{ old('name', $permission->name) }}" required
                                   class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md @error('name') border-red-300 @enderror"
                                   {{ in_array($permission->name, ['manage_users', 'manage_roles', 'manage_permissions', 'manage_plugins', 'view_dashboard']) ? 'readonly' : '' }}>
                        </div>
                        @if(in_array($permission->name, ['manage_users', 'manage_roles', 'manage_permissions', 'manage_plugins', 'view_dashboard']))
                            <p class="mt-2 text-sm text-yellow-600">System permission names cannot be changed.</p>
                        @else
                            <p class="mt-2 text-sm text-gray-500">Lowercase letters, numbers, and underscores only. Used internally by the system.</p>
                        @endif
                        @error('name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Plugin -->
                    <div>
                        <label for="plugin" class="block text-sm font-medium text-gray-700">
                            Plugin
                        </label>
                        <div class="mt-1">
                            <select name="plugin" id="plugin"
                                    class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md @error('plugin') border-red-300 @enderror">
                                <option value="">System Permission</option>
                                @php
                                    $pluginManager = app(\App\Services\PluginManager::class);
                                    $enabledPlugins = $pluginManager->getEnabledPlugins();
                                @endphp
                                @foreach($enabledPlugins as $pluginName => $plugin)
                                    <option value="{{ $pluginName }}" {{ old('plugin', $permission->plugin) == $pluginName ? 'selected' : '' }}>
                                        {{ $plugin->config['display_name'] ?? ucfirst($pluginName) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Select the plugin this permission belongs to, or leave empty for system permissions.</p>
                        @error('plugin')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">
                            Description
                        </label>
                        <div class="mt-1">
                            <textarea name="description" id="description" rows="3"
                                      class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md @error('description') border-red-300 @enderror"
                                      placeholder="Describe what this permission allows users to do...">{{ old('description', $permission->description) }}</textarea>
                        </div>
                        @error('description')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{{ route('permissions.show', $permission) }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-save mr-2"></i>
                        Update Permission
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Current Roles -->
    @if($permission->roles->count() > 0)
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Roles with this Permission</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">{{ $permission->roles->count() }} {{ Str::plural('role', $permission->roles->count()) }} currently have this permission.</p>
            </div>
            <div class="border-t border-gray-200">
                <div class="px-4 py-5 sm:px-6">
                    <div class="space-y-3">
                        @foreach($permission->roles as $role)
                            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-user-shield text-2xl text-blue-500"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-blue-900">{{ $role->display_name }}</p>
                                        <p class="text-xs text-blue-700">{{ $role->name }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ $role->users->count() }} {{ Str::plural('user', $role->users->count()) }}
                                    </span>
                                    <a href="{{ route('roles.show', $role) }}" class="text-blue-600 hover:text-blue-900" title="View Role">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Permission Type Information -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Permission Information</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Additional details about this permission.</p>
        </div>
        <div class="border-t border-gray-200">
            <dl>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Permission Type</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        @php
                            $systemPermissions = ['manage_users', 'manage_roles', 'manage_permissions', 'manage_plugins', 'view_dashboard'];
                        @endphp
                        @if(in_array($permission->name, $systemPermissions))
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-cog mr-1"></i>
                                System Permission
                            </span>
                            <p class="mt-1 text-xs text-gray-500">This is a core system permission required for application functionality.</p>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-user mr-1"></i>
                                Custom Permission
                            </span>
                            <p class="mt-1 text-xs text-gray-500">This is a custom permission that can be modified or deleted.</p>
                        @endif
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $permission->created_at->format('F j, Y \a\t g:i A') }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $permission->updated_at->format('F j, Y \a\t g:i A') }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Assigned to Roles</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $permission->roles->count() }} {{ Str::plural('role', $permission->roles->count()) }}</dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- Help Text -->
    @if(!in_array($permission->name, ['manage_users', 'manage_roles', 'manage_permissions', 'manage_plugins', 'view_dashboard']))
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">
                        Permission Editing Guidelines
                    </h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Changes to the display name will be reflected immediately in the interface</li>
                            <li>System name changes may affect code that checks for this permission</li>
                            <li>Updating the description helps other administrators understand the permission's purpose</li>
                            <li>Roles that have this permission will retain it after updates</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection
