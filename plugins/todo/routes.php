<?php

use Illuminate\Support\Facades\Route;
use Plugins\Todo\Controllers\TodoController;

Route::middleware(['web', 'auth'])->group(function () {
    Route::prefix('todo')->name('todo.')->group(function () {
        Route::get('/', [TodoController::class, 'index'])->name('index');
        Route::post('/', [TodoController::class, 'store'])->name('store');
        Route::get('/{todo}', [TodoController::class, 'show'])->name('show');
        Route::put('/{todo}', [TodoController::class, 'update'])->name('update');
        Route::delete('/{todo}', [TodoController::class, 'destroy'])->name('destroy');
        
        // AJAX routes for dynamic functionality
        Route::post('/reorder', [TodoController::class, 'reorder'])->name('reorder');
        Route::post('/{todo}/toggle', [TodoController::class, 'toggle'])->name('toggle');
        Route::get('/api/list', [TodoController::class, 'apiList'])->name('api.list');
    });
});
