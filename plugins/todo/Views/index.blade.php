@extends('layouts.app')

@section('head')
<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
        max-height: 2.8em; /* 2 lines * 1.4 line-height */
    }

    /* Quill Editor Container Styling */
    .quill-editor-container {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
    }

    .quill-editor-container:focus-within {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-1px);
    }

    /* Quill Editor Toolbar Styling */
    .ql-toolbar {
        border: 1px solid #e5e7eb !important;
        border-bottom: 1px solid #d1d5db !important;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
        padding: 16px 20px !important;
        border-radius: 12px 12px 0 0 !important;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        position: relative;
    }

    .ql-toolbar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
    }

    .dark .ql-toolbar {
        border-color: #374151 !important;
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
    }

    .dark .ql-toolbar::before {
        background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.4), transparent);
    }

    /* Quill Editor Button Styling */
    .ql-toolbar .ql-formats {
        margin-right: 15px;
    }

    .ql-toolbar button {
        padding: 8px 10px !important;
        border: 1px solid rgba(0, 0, 0, 0.08) !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        color: #374151 !important;
        font-size: 14px !important;
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
        margin: 2px !important;
        width: 36px !important;
        height: 36px !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    }

    .ql-toolbar button:hover {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
        color: #1e293b !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        border-color: rgba(59, 130, 246, 0.2) !important;
    }

    .ql-toolbar button.ql-active {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
        color: white !important;
        border-color: #2563eb !important;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
    }

    .dark .ql-toolbar button {
        background: linear-gradient(135deg, #4b5563 0%, #374151 100%) !important;
        color: #e2e8f0 !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    }

    .dark .ql-toolbar button:hover {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
        color: #ffffff !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        border-color: rgba(96, 165, 250, 0.3) !important;
    }

    .dark .ql-toolbar button.ql-active {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
        color: #ffffff !important;
        border-color: #2563eb !important;
    }

    /* Quill Editor Dropdown Styling */
    .ql-toolbar .ql-picker {
        color: #374151 !important;
    }

    .ql-toolbar .ql-picker-label {
        border: 1px solid rgba(0, 0, 0, 0.08) !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
        border-radius: 8px !important;
        padding: 8px 12px !important;
        font-size: 14px !important;
        color: #374151 !important;
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    }

    .ql-toolbar .ql-picker-label:hover {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
        border-color: rgba(59, 130, 246, 0.2) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }

    .dark .ql-toolbar .ql-picker-label {
        background: linear-gradient(135deg, #4b5563 0%, #374151 100%) !important;
        color: #e2e8f0 !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    }

    .dark .ql-toolbar .ql-picker-label:hover {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
        color: #ffffff !important;
        border-color: rgba(96, 165, 250, 0.3) !important;
    }

    /* Quill Editor Content Styling */
    .ql-editor {
        border: 1px solid #e5e7eb !important;
        border-top: none !important;
        border-radius: 0 0 12px 12px !important;
        min-height: 200px !important;
        padding: 24px !important;
        font-size: 16px !important;
        line-height: 1.6 !important;
        color: #374151 !important;
        background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        position: relative;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    }

    .ql-editor::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .ql-editor:focus::before {
        opacity: 1;
    }

    .dark .ql-editor {
        border-color: #374151 !important;
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
        color: #f9fafb !important;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    }

    .dark .ql-editor::before {
        background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.3), transparent);
    }

    .ql-editor:focus {
        border-color: #3b82f6 !important;
        box-shadow:
            inset 0 1px 2px rgba(0, 0, 0, 0.05),
            0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    }

    .dark .ql-editor:focus {
        border-color: #3b82f6 !important;
        box-shadow:
            inset 0 1px 2px rgba(0, 0, 0, 0.2),
            0 0 0 3px rgba(59, 130, 246, 0.2) !important;
        background: linear-gradient(135deg, #1f2937 0%, #0f172a 100%) !important;
    }

    /* Quill Editor Content Typography */
    .ql-editor h1, .ql-editor h2, .ql-editor h3 {
        font-weight: 700 !important;
        margin: 24px 0 12px 0 !important;
        line-height: 1.25 !important;
        color: #1e293b !important;
    }

    .ql-editor h1 {
        font-size: 28px !important;
        border-bottom: 2px solid #e2e8f0 !important;
        padding-bottom: 8px !important;
    }

    .ql-editor h2 {
        font-size: 22px !important;
        border-bottom: 1px solid #f1f5f9 !important;
        padding-bottom: 4px !important;
    }

    .ql-editor h3 {
        font-size: 18px !important;
        color: #3b82f6 !important;
    }

    .dark .ql-editor h1,
    .dark .ql-editor h2,
    .dark .ql-editor h3 {
        color: #f1f5f9 !important;
    }

    .dark .ql-editor h1 {
        border-bottom-color: #374151 !important;
    }

    .dark .ql-editor h2 {
        border-bottom-color: #1f2937 !important;
    }

    .dark .ql-editor h3 {
        color: #60a5fa !important;
    }

    .ql-editor p {
        margin: 12px 0 !important;
    }

    .ql-editor ul, .ql-editor ol {
        margin: 16px 0 !important;
        padding-left: 28px !important;
    }

    .ql-editor li {
        margin: 8px 0 !important;
        line-height: 1.6 !important;
    }

    .ql-editor blockquote {
        border-left: 4px solid #3b82f6 !important;
        padding: 16px 20px !important;
        margin: 20px 0 !important;
        font-style: italic !important;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
        border-radius: 0 8px 8px 0 !important;
        color: #475569 !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    }

    .dark .ql-editor blockquote {
        border-left-color: #60a5fa !important;
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
        color: #d1d5db !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    }

    .ql-editor a {
        color: #3b82f6 !important;
        text-decoration: none !important;
        border-bottom: 1px solid transparent !important;
        transition: all 0.2s ease !important;
        font-weight: 500 !important;
    }

    .ql-editor a:hover {
        border-bottom-color: #3b82f6 !important;
        background: rgba(59, 130, 246, 0.05) !important;
        padding: 2px 4px !important;
        margin: -2px -4px !important;
        border-radius: 4px !important;
    }

    .dark .ql-editor a {
        color: #60a5fa !important;
    }

    .dark .ql-editor a:hover {
        border-bottom-color: #60a5fa !important;
        background: rgba(96, 165, 250, 0.1) !important;
    }

    .ql-editor code {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
        padding: 4px 8px !important;
        border-radius: 6px !important;
        font-family: 'SF Mono', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
        font-size: 13px !important;
        border: 1px solid #e2e8f0 !important;
        color: #be185d !important;
        font-weight: 500 !important;
    }

    .dark .ql-editor code {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%) !important;
        border-color: #4b5563 !important;
        color: #f472b6 !important;
    }

    /* Enhanced focus and interaction states */
    .quill-editor-container:hover {
        box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1) !important;
    }

    /* Smooth scrollbar for editor content */
    .ql-editor::-webkit-scrollbar {
        width: 8px;
    }

    .ql-editor::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 4px;
    }

    .ql-editor::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #cbd5e1, #94a3b8);
        border-radius: 4px;
    }

    .ql-editor::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #94a3b8, #64748b);
    }

    .dark .ql-editor::-webkit-scrollbar-track {
        background: #1f2937;
    }

    .dark .ql-editor::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #4b5563, #6b7280);
    }

    .dark .ql-editor::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #6b7280, #9ca3af);
    }
</style>
@endsection

@section('head')
<style>
.sortable-ghost {
    opacity: 0.4;
    background: #f3f4f6;
    border: 2px dashed #d1d5db;
}

.sortable-chosen {
    background: #eff6ff;
    border: 2px solid #3b82f6;
}

.sortable-drag {
    background: #ffffff;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: rotate(2deg);
}

.todo-item {
    transition: all 0.3s ease;
}

.todo-item.completed {
    opacity: 0.6;
}

.todo-item.completed .todo-title {
    text-decoration: line-through;
}

.todo-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateX(0); }
    to { opacity: 0; transform: translateX(-100%); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                <i class="fas fa-tasks mr-3 text-blue-600 dark:text-blue-400"></i>
                Todo List
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Manage your tasks and stay organized</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="showCreateModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium rounded-md transition duration-150 ease-in-out">
                <i class="fas fa-plus mr-2"></i>
                Add Todo
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Total</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['total'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Pending</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['pending'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Completed</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['completed'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Overdue</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['overdue'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-fire text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">High Priority</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['high_priority'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Controls -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="showCompleted" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="showCompleted" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Show completed tasks</label>
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <label for="priorityFilter" class="text-sm text-gray-700 dark:text-gray-300">Priority:</label>
                    <select id="priorityFilter" class="border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm">
                        <option value="all">All Priorities</option>
                        <option value="high">High Priority</option>
                        <option value="medium">Medium Priority</option>
                        <option value="low">Low Priority</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Todo List -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Your Tasks</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Drag and drop to reorder your tasks</p>
        </div>
        <div class="px-4 py-5 sm:px-6">
            <div id="todo-list" class="space-y-3">
                <!-- Todo items will be loaded here -->
            </div>
            <div id="empty-state" class="text-center py-12 hidden">
                <i class="fas fa-tasks text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No tasks found</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">Get started by creating your first todo item.</p>
                <button onclick="showCreateModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                    <i class="fas fa-plus mr-2"></i>
                    Add Your First Todo
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<div id="todoModal" class="fixed inset-0 bg-gray-600 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-5 mx-auto p-6 border border-gray-200 dark:border-gray-700 max-w-4xl h-[85vh] shadow-lg rounded-md bg-white dark:bg-gray-800 flex flex-col">
        <div class="flex-shrink-0 mb-4">
            <h3 id="modalTitle" class="text-xl leading-6 font-medium text-gray-900 dark:text-white">Add New Todo</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Create or edit a todo item</p>
        </div>

        <form id="todoForm" class="flex flex-col h-full">
            <input type="hidden" id="todoId" name="id">

            <!-- First Row: Title, Priority, Due Date -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                    <label for="todoTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Title *</label>
                    <input type="text" id="todoTitle" name="title" required
                           class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                           placeholder="Enter todo title">
                </div>

                <div>
                    <label for="todoPriority" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priority *</label>
                    <select id="todoPriority" name="priority" required
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="low">Low Priority</option>
                        <option value="medium" selected>Medium Priority</option>
                        <option value="high">High Priority</option>
                    </select>
                </div>

                <div>
                    <label for="todoDueDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Due Date</label>
                    <input type="datetime-local" id="todoDueDate" name="due_date"
                           class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </div>
            </div>

            <!-- Second Row: Description Editor (Full Width & Height) -->
            <div class="flex-1 flex flex-col">
                <label for="todoDescriptionEditor" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                <div class="quill-editor-container flex-1 flex flex-col">
                    <div id="todoDescriptionEditor" class="flex-1 min-h-[200px]"></div>
                    <textarea id="todoDescriptionHidden" name="description" style="display: none;"></textarea>
                </div>
                <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Rich text editor with formatting options. Only first 2 lines will be shown in the todo list.</p>
            </div>

            <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 mt-4">
                <button type="button" onclick="hideModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Cancel
                </button>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-save mr-2"></i>
                    <span id="submitButtonText">Create Todo</span>
                </button>
            </div>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
let todos = @json($todos);
let isEditing = false;
let editingId = null;
let sortable = null;

// Helper function to strip HTML tags for display
function stripHtml(html) {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
}

// Quill editor instance
let quillEditor = null;

// Initialize Quill editor
function initializeTodoQuillEditor() {
    if (typeof window.initializeQuillEditor === 'function') {
        quillEditor = window.initializeQuillEditor();
        return quillEditor;
    } else {
        console.error('Quill editor module not loaded');
        return null;
    }
}

function syncEditorContent() {
    if (quillEditor) {
        const hiddenField = document.getElementById('todoDescriptionHidden');
        hiddenField.value = quillEditor.root.innerHTML;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

function initializePage() {
    renderTodos();
    initializeSortable();
    initializeEventListeners();
}

function initializeEventListeners() {
    // Initialize Quill editor
    quillEditor = initializeTodoQuillEditor();

    // Show completed toggle
    document.getElementById('showCompleted').addEventListener('change', function() {
        renderTodos();
    });

    // Priority filter
    document.getElementById('priorityFilter').addEventListener('change', function() {
        renderTodos();
    });

    // Form submission
    document.getElementById('todoForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitTodo();
    });
}

function initializeSortable() {
    const todoList = document.getElementById('todo-list');
    if (sortable) {
        sortable.destroy();
    }

    sortable = Sortable.create(todoList, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        onEnd: function(evt) {
            reorderTodos();
        }
    });
}

function renderTodos() {
    const todoList = document.getElementById('todo-list');
    const emptyState = document.getElementById('empty-state');
    const showCompleted = document.getElementById('showCompleted').checked;
    const priorityFilter = document.getElementById('priorityFilter').value;

    // Filter todos
    let filteredTodos = todos.filter(todo => {
        if (!showCompleted && todo.is_completed) {
            return false;
        }
        if (priorityFilter !== 'all' && todo.priority !== priorityFilter) {
            return false;
        }
        return true;
    });

    if (filteredTodos.length === 0) {
        todoList.innerHTML = '';
        emptyState.classList.remove('hidden');
        return;
    }

    emptyState.classList.add('hidden');
    todoList.innerHTML = filteredTodos.map(todo => createTodoElement(todo)).join('');

    // Reinitialize sortable after rendering
    initializeSortable();
}

function createTodoElement(todo) {
    const isOverdue = todo.due_date && new Date(todo.due_date) < new Date() && !todo.is_completed;
    const dueDate = todo.due_date ? new Date(todo.due_date).toLocaleDateString() : null;

    return `
        <div class="todo-item ${todo.is_completed ? 'completed' : ''} bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 cursor-move" data-id="${todo.id}">
            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-3 flex-1">
                    <div class="flex items-center pt-1">
                        <input type="checkbox" ${todo.is_completed ? 'checked' : ''}
                               onchange="toggleTodo(${todo.id})"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    </div>

                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-1">
                            <h4 class="todo-title text-lg font-medium text-gray-900 dark:text-white">${todo.title}</h4>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadgeClass(todo.priority)}">
                                ${todo.priority.charAt(0).toUpperCase() + todo.priority.slice(1)}
                            </span>
                            ${isOverdue ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"><i class="fas fa-exclamation-triangle mr-1"></i>Overdue</span>' : ''}
                        </div>

                        ${todo.description ? `<p class="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2" title="${stripHtml(todo.description)}">${stripHtml(todo.description)}</p>` : ''}

                        <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                            ${dueDate ? `<span><i class="fas fa-calendar mr-1"></i>Due: ${dueDate}</span>` : ''}
                            ${todo.completed_at ? `<span><i class="fas fa-check mr-1"></i>Completed: ${new Date(todo.completed_at).toLocaleDateString()}</span>` : ''}
                        </div>
                    </div>
                </div>

                <div class="flex items-center space-x-2 ml-4">
                    <button onclick="editTodo(${todo.id})" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="deleteTodo(${todo.id})" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

function getPriorityBadgeClass(priority) {
    const classes = {
        'high': 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
        'medium': 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
        'low': 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
    };
    return classes[priority] || 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
}

function showCreateModal() {
    isEditing = false;
    editingId = null;
    document.getElementById('modalTitle').textContent = 'Add New Todo';
    document.getElementById('submitButtonText').textContent = 'Create Todo';
    document.getElementById('todoForm').reset();
    document.getElementById('todoId').value = '';

    // Clear Quill editor content
    if (quillEditor) {
        if (typeof window.clearQuillContent === 'function') {
            window.clearQuillContent(quillEditor);
        } else {
            quillEditor.setContents([]);
        }
    }
    document.getElementById('todoDescriptionHidden').value = '';

    document.getElementById('todoModal').classList.remove('hidden');
}

function editTodo(id) {
    const todo = todos.find(t => t.id === id);
    if (!todo) return;

    isEditing = true;
    editingId = id;
    document.getElementById('modalTitle').textContent = 'Edit Todo';
    document.getElementById('submitButtonText').textContent = 'Update Todo';

    document.getElementById('todoId').value = todo.id;
    document.getElementById('todoTitle').value = todo.title;
    document.getElementById('todoPriority').value = todo.priority;

    // Set Quill editor content
    if (quillEditor && todo.description) {
        if (typeof window.setQuillContent === 'function') {
            window.setQuillContent(quillEditor, todo.description);
        } else {
            quillEditor.root.innerHTML = todo.description;
        }
    }
    document.getElementById('todoDescriptionHidden').value = todo.description || '';

    if (todo.due_date) {
        const date = new Date(todo.due_date);
        document.getElementById('todoDueDate').value = date.toISOString().slice(0, 16);
    }

    document.getElementById('todoModal').classList.remove('hidden');
}

function hideModal() {
    document.getElementById('todoModal').classList.add('hidden');
    document.getElementById('todoForm').reset();

    // Clear Quill editor content
    if (quillEditor) {
        if (typeof window.clearQuillContent === 'function') {
            window.clearQuillContent(quillEditor);
        } else {
            quillEditor.setContents([]);
        }
    }
    document.getElementById('todoDescriptionHidden').value = '';

    isEditing = false;
    editingId = null;
}

function submitTodo() {
    // Sync editor content to hidden field
    syncEditorContent();

    const formData = new FormData(document.getElementById('todoForm'));
    const data = Object.fromEntries(formData.entries());

    // For updates, use POST with _method spoofing
    const url = isEditing ? `/todo/${editingId}` : '/todo';

    if (isEditing) {
        data._method = 'PUT';
    }

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            hideModal();
            refreshTodos();
        } else {
            showNotification(data.message || 'An error occurred while saving the todo', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while saving the todo', 'error');
    });
}

function toggleTodo(id) {
    fetch(`/todo/${id}/toggle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');

            // Update local todo data
            const todoIndex = todos.findIndex(t => t.id === id);
            if (todoIndex !== -1) {
                todos[todoIndex] = data.todo;
            }

            // Add fade effect for completed items
            const todoElement = document.querySelector(`[data-id="${id}"]`);
            if (data.todo.is_completed && !document.getElementById('showCompleted').checked) {
                todoElement.classList.add('fade-out');
                setTimeout(() => {
                    renderTodos();
                }, 500);
            } else {
                renderTodos();
            }
        } else {
            showNotification(data.message, 'error');
            // Revert checkbox state
            const checkbox = document.querySelector(`[data-id="${id}"] input[type="checkbox"]`);
            checkbox.checked = !checkbox.checked;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while updating the todo', 'error');
        // Revert checkbox state
        const checkbox = document.querySelector(`[data-id="${id}"] input[type="checkbox"]`);
        checkbox.checked = !checkbox.checked;
    });
}

function deleteTodo(id) {
    const todo = todos.find(t => t.id === id);
    if (!todo) return;

    if (!confirm(`Are you sure you want to delete "${todo.title}"?`)) {
        return;
    }

    fetch(`/todo/${id}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');

            // Add fade effect
            const todoElement = document.querySelector(`[data-id="${id}"]`);
            todoElement.classList.add('fade-out');

            setTimeout(() => {
                refreshTodos();
            }, 500);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while deleting the todo', 'error');
    });
}

function reorderTodos() {
    const todoElements = document.querySelectorAll('#todo-list .todo-item');
    const todoIds = Array.from(todoElements).map(el => parseInt(el.dataset.id));

    fetch('/todo/reorder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ todo_ids: todoIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            refreshTodos();
        } else {
            showNotification(data.message, 'error');
            renderTodos(); // Revert to original order
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while reordering todos', 'error');
        renderTodos(); // Revert to original order
    });
}

function refreshTodos() {
    const showCompleted = document.getElementById('showCompleted').checked;
    const priority = document.getElementById('priorityFilter').value;

    const params = new URLSearchParams({
        show_completed: showCompleted,
        priority: priority
    });

    fetch(`/todo/api/list?${params}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            todos = data.todos;
            renderTodos();
        }
    })
    .catch(error => {
        console.error('Error refreshing todos:', error);
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 transform translate-x-full`;

    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
    notification.className += ` ${bgColor} text-white`;

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('todoModal');
    if (e.target === modal) {
        hideModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        hideModal();
    }
});

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    renderTodos();
    initializeSortable();

    // Add event listener to sync editor content
    const editor = document.getElementById('todoDescription');
    if (editor) {
        editor.addEventListener('input', syncEditorContent);
        editor.addEventListener('paste', function() {
            setTimeout(syncEditorContent, 10);
        });

        // Update toolbar state on selection change
        editor.addEventListener('mouseup', updateToolbarState);
        editor.addEventListener('keyup', updateToolbarState);
        editor.addEventListener('focus', updateToolbarState);

        // Handle keyboard shortcuts
        editor.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'b':
                        e.preventDefault();
                        formatText('bold');
                        break;
                    case 'i':
                        e.preventDefault();
                        formatText('italic');
                        break;
                    case 'u':
                        e.preventDefault();
                        formatText('underline');
                        break;
                    case 'z':
                        if (e.shiftKey) {
                            e.preventDefault();
                            formatText('redo');
                        } else {
                            e.preventDefault();
                            formatText('undo');
                        }
                        break;
                    case 'y':
                        e.preventDefault();
                        formatText('redo');
                        break;
                }
            }
        });
    }
});
</script>
@endsection
