@extends('layouts.app')

@section('head')
<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
        max-height: 2.8em; /* 2 lines * 1.4 line-height */
    }

    .text-editor {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
    }

    .text-editor:focus-within {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-1px);
    }

    .editor-toolbar {
        border: 1px solid #e5e7eb;
        border-bottom: 1px solid #d1d5db;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 16px 20px;
        border-radius: 12px 12px 0 0;
        display: flex !important;
        flex-direction: row !important;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
        position: relative;
    }

    .editor-toolbar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
    }

    .dark .editor-toolbar {
        border-color: #374151 !important;
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
    }

    .dark .editor-toolbar::before {
        background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.4), transparent);
    }

    .editor-btn {
        padding: 10px 12px !important;
        border: 1px solid rgba(0, 0, 0, 0.08) !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px !important;
        color: #475569 !important;
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
        display: inline-flex !important;
        align-items: center;
        justify-content: center;
        min-width: 40px;
        height: 40px;
        margin: 0 3px !important;
        vertical-align: middle;
        position: relative;
        font-weight: 500;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .editor-btn::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 8px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
        opacity: 0;
        transition: opacity 0.25s ease;
    }

    .editor-btn:hover {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
        color: #1e293b !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-color: rgba(59, 130, 246, 0.2) !important;
    }

    .editor-btn:hover::before {
        opacity: 1;
    }

    .editor-btn:active {
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%) !important;
        transform: translateY(0px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .editor-btn.active {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
        color: white !important;
        border-color: #2563eb !important;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        transform: translateY(-1px);
    }

    .editor-btn.active:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
    }

    .dark .editor-btn {
        background: linear-gradient(135deg, #4b5563 0%, #374151 100%) !important;
        color: #e2e8f0 !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .dark .editor-btn:hover {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
        color: #ffffff !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        border-color: rgba(96, 165, 250, 0.3) !important;
    }

    .dark .editor-btn:active {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%) !important;
        color: #ffffff !important;
    }

    .dark .editor-btn.active {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
        color: #ffffff !important;
        border-color: #2563eb !important;
    }

    .dark .editor-btn.active:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
        color: #ffffff !important;
    }

    .toolbar-separator {
        width: 2px;
        height: 32px;
        background: linear-gradient(180deg, transparent 0%, #d1d5db 20%, #d1d5db 80%, transparent 100%);
        margin: 0 12px;
        flex-shrink: 0;
        border-radius: 1px;
        position: relative;
    }

    .toolbar-separator::before {
        content: '';
        position: absolute;
        left: 1px;
        top: 0;
        bottom: 0;
        width: 1px;
        background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.8) 20%, rgba(255, 255, 255, 0.8) 80%, transparent 100%);
    }

    .dark .toolbar-separator {
        background: linear-gradient(180deg, transparent 0%, #4b5563 20%, #4b5563 80%, transparent 100%) !important;
    }

    .dark .toolbar-separator::before {
        background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.1) 20%, rgba(255, 255, 255, 0.1) 80%, transparent 100%);
    }

    .editor-dropdown {
        padding: 8px 16px !important;
        border: 1px solid rgba(0, 0, 0, 0.08) !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px !important;
        color: #475569 !important;
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
        margin: 0 3px !important;
        min-width: 120px;
        height: 40px;
        font-weight: 500;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 12px center;
        background-repeat: no-repeat;
        background-size: 16px;
        padding-right: 40px !important;
    }

    .editor-dropdown:hover {
        border-color: rgba(59, 130, 246, 0.2) !important;
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }

    .editor-dropdown:focus {
        outline: none;
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .dark .editor-dropdown {
        border-color: rgba(255, 255, 255, 0.1) !important;
        background: linear-gradient(135deg, #4b5563 0%, #374151 100%) !important;
        color: #e2e8f0 !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%9ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    }

    .dark .editor-dropdown:hover {
        border-color: rgba(96, 165, 250, 0.3) !important;
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
        color: #ffffff !important;
    }

    .dark .editor-dropdown:focus {
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }

    /* Ensure FontAwesome icons display properly */
    .editor-btn i {
        font-size: 14px !important;
        line-height: 1 !important;
        color: inherit !important;
    }

    /* Force horizontal layout and override any conflicting styles */
    .text-editor .editor-toolbar {
        width: 100%;
        overflow-x: auto;
    }

    .text-editor .editor-toolbar > * {
        flex-shrink: 0;
    }

    /* Ensure proper spacing between toolbar items */
    .editor-toolbar .editor-btn + .editor-btn {
        margin-left: 2px;
    }

    .editor-toolbar .toolbar-separator + .editor-btn {
        margin-left: 0;
    }

    /* Override any global button styles that might interfere */
    .editor-toolbar button {
        font-family: inherit !important;
        font-weight: normal !important;
        text-transform: none !important;
        letter-spacing: normal !important;
    }

    .editor-content {
        border: 1px solid #e5e7eb;
        border-top: none;
        border-radius: 0 0 12px 12px;
        min-height: 200px;
        padding: 24px;
        background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
        outline: none;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', Roboto, sans-serif;
        font-size: 15px;
        line-height: 1.7;
        overflow-y: auto;
        flex: 1;
        color: #1f2937 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .editor-content::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .editor-content:focus::before {
        opacity: 1;
    }

    .dark .editor-content {
        border-color: #374151 !important;
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
        color: #f9fafb !important;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .dark .editor-content::before {
        background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.3), transparent);
    }

    .editor-content:focus {
        border-color: #3b82f6 !important;
        box-shadow:
            inset 0 1px 2px rgba(0, 0, 0, 0.05),
            0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    }

    .dark .editor-content:focus {
        border-color: #3b82f6 !important;
        box-shadow:
            inset 0 1px 2px rgba(0, 0, 0, 0.2),
            0 0 0 3px rgba(59, 130, 246, 0.2) !important;
        background: linear-gradient(135deg, #1f2937 0%, #0f172a 100%) !important;
    }

    .editor-content:empty:before {
        content: attr(data-placeholder);
        color: #9ca3af;
        pointer-events: none;
        font-style: italic;
        opacity: 0.7;
        font-weight: 400;
    }

    .dark .editor-content:empty:before {
        color: #6b7280;
    }

    /* Enhanced text formatting styles */
    .editor-content h1, .editor-content h2, .editor-content h3 {
        font-weight: 700;
        margin: 24px 0 12px 0;
        line-height: 1.25;
        color: #1e293b;
        position: relative;
    }

    .editor-content h1 {
        font-size: 28px;
        border-bottom: 2px solid #e2e8f0;
        padding-bottom: 8px;
    }
    .editor-content h2 {
        font-size: 22px;
        border-bottom: 1px solid #f1f5f9;
        padding-bottom: 4px;
    }
    .editor-content h3 {
        font-size: 18px;
        color: #3b82f6;
    }

    .dark .editor-content h1,
    .dark .editor-content h2,
    .dark .editor-content h3 {
        color: #f1f5f9;
    }

    .dark .editor-content h1 {
        border-bottom-color: #374151;
    }

    .dark .editor-content h2 {
        border-bottom-color: #1f2937;
    }

    .dark .editor-content h3 {
        color: #60a5fa;
    }

    .editor-content p {
        margin: 12px 0;
        text-align: justify;
    }

    .editor-content ul, .editor-content ol {
        margin: 16px 0;
        padding-left: 28px;
    }

    .editor-content li {
        margin: 8px 0;
        line-height: 1.6;
    }

    .editor-content ul li {
        list-style-type: none;
        position: relative;
    }

    .editor-content ul li::before {
        content: '•';
        color: #3b82f6;
        font-weight: bold;
        position: absolute;
        left: -20px;
        font-size: 16px;
    }

    .dark .editor-content ul li::before {
        color: #60a5fa;
    }

    .editor-content ol li {
        counter-increment: item;
    }

    .editor-content blockquote {
        border-left: 4px solid #3b82f6;
        padding: 16px 20px;
        margin: 20px 0;
        font-style: italic;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 0 8px 8px 0;
        color: #475569;
        position: relative;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .editor-content blockquote::before {
        content: '"';
        font-size: 48px;
        color: #3b82f6;
        position: absolute;
        top: -8px;
        left: 12px;
        opacity: 0.3;
        font-family: Georgia, serif;
    }

    .dark .editor-content blockquote {
        border-left-color: #60a5fa;
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        color: #d1d5db;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .dark .editor-content blockquote::before {
        color: #60a5fa;
    }

    .editor-content a {
        color: #3b82f6;
        text-decoration: none;
        border-bottom: 1px solid transparent;
        transition: all 0.2s ease;
        font-weight: 500;
    }

    .editor-content a:hover {
        border-bottom-color: #3b82f6;
        background: rgba(59, 130, 246, 0.05);
        padding: 2px 4px;
        margin: -2px -4px;
        border-radius: 4px;
    }

    .dark .editor-content a {
        color: #60a5fa;
    }

    .dark .editor-content a:hover {
        border-bottom-color: #60a5fa;
        background: rgba(96, 165, 250, 0.1);
    }

    .editor-content code {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        padding: 4px 8px;
        border-radius: 6px;
        font-family: 'SF Mono', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        border: 1px solid #e2e8f0;
        color: #be185d;
        font-weight: 500;
    }

    .dark .editor-content code {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        border-color: #4b5563;
        color: #f472b6;
    }

    .editor-content hr {
        border: none;
        height: 2px;
        background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
        margin: 24px 0;
    }

    .dark .editor-content hr {
        background: linear-gradient(90deg, transparent, #374151, transparent);
    }

    /* Selection styling */
    .editor-content ::selection {
        background: rgba(59, 130, 246, 0.2);
        color: inherit;
    }

    .dark .editor-content ::selection {
        background: rgba(96, 165, 250, 0.3);
    }

    /* Editor group styling */
    .editor-group {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    /* Enhanced focus and interaction states */
    .text-editor:hover {
        box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    }

    /* Smooth scrollbar for editor content */
    .editor-content::-webkit-scrollbar {
        width: 8px;
    }

    .editor-content::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 4px;
    }

    .editor-content::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #cbd5e1, #94a3b8);
        border-radius: 4px;
    }

    .editor-content::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #94a3b8, #64748b);
    }

    .dark .editor-content::-webkit-scrollbar-track {
        background: #1f2937;
    }

    .dark .editor-content::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #4b5563, #6b7280);
    }

    .dark .editor-content::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #6b7280, #9ca3af);
    }
</style>
@endsection

@section('head')
<style>
.sortable-ghost {
    opacity: 0.4;
    background: #f3f4f6;
    border: 2px dashed #d1d5db;
}

.sortable-chosen {
    background: #eff6ff;
    border: 2px solid #3b82f6;
}

.sortable-drag {
    background: #ffffff;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: rotate(2deg);
}

.todo-item {
    transition: all 0.3s ease;
}

.todo-item.completed {
    opacity: 0.6;
}

.todo-item.completed .todo-title {
    text-decoration: line-through;
}

.todo-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateX(0); }
    to { opacity: 0; transform: translateX(-100%); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                <i class="fas fa-tasks mr-3 text-blue-600 dark:text-blue-400"></i>
                Todo List
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Manage your tasks and stay organized</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="showCreateModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium rounded-md transition duration-150 ease-in-out">
                <i class="fas fa-plus mr-2"></i>
                Add Todo
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Total</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['total'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Pending</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['pending'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Completed</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['completed'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Overdue</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['overdue'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-fire text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">High Priority</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['high_priority'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Controls -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="showCompleted" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="showCompleted" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Show completed tasks</label>
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <label for="priorityFilter" class="text-sm text-gray-700 dark:text-gray-300">Priority:</label>
                    <select id="priorityFilter" class="border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm">
                        <option value="all">All Priorities</option>
                        <option value="high">High Priority</option>
                        <option value="medium">Medium Priority</option>
                        <option value="low">Low Priority</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Todo List -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Your Tasks</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Drag and drop to reorder your tasks</p>
        </div>
        <div class="px-4 py-5 sm:px-6">
            <div id="todo-list" class="space-y-3">
                <!-- Todo items will be loaded here -->
            </div>
            <div id="empty-state" class="text-center py-12 hidden">
                <i class="fas fa-tasks text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No tasks found</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">Get started by creating your first todo item.</p>
                <button onclick="showCreateModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                    <i class="fas fa-plus mr-2"></i>
                    Add Your First Todo
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<div id="todoModal" class="fixed inset-0 bg-gray-600 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-5 mx-auto p-6 border border-gray-200 dark:border-gray-700 max-w-4xl h-[85vh] shadow-lg rounded-md bg-white dark:bg-gray-800 flex flex-col">
        <div class="flex-shrink-0 mb-4">
            <h3 id="modalTitle" class="text-xl leading-6 font-medium text-gray-900 dark:text-white">Add New Todo</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Create or edit a todo item</p>
        </div>

        <form id="todoForm" class="flex flex-col h-full">
            <input type="hidden" id="todoId" name="id">

            <!-- First Row: Title, Priority, Due Date -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                    <label for="todoTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Title *</label>
                    <input type="text" id="todoTitle" name="title" required
                           class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                           placeholder="Enter todo title">
                </div>

                <div>
                    <label for="todoPriority" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priority *</label>
                    <select id="todoPriority" name="priority" required
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="low">Low Priority</option>
                        <option value="medium" selected>Medium Priority</option>
                        <option value="high">High Priority</option>
                    </select>
                </div>

                <div>
                    <label for="todoDueDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Due Date</label>
                    <input type="datetime-local" id="todoDueDate" name="due_date"
                           class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </div>
            </div>

            <!-- Second Row: Description Editor (Full Width & Height) -->
            <div class="flex-1 flex flex-col">
                <label for="todoDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                <div class="text-editor flex-1 flex flex-col">
                    <div class="editor-toolbar">
                        <!-- Text Formatting Group -->
                        <div class="editor-group">
                            <button type="button" class="editor-btn" onclick="formatText('bold')" title="Bold (Ctrl+B)">
                                <i class="fas fa-bold"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('italic')" title="Italic (Ctrl+I)">
                                <i class="fas fa-italic"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('underline')" title="Underline (Ctrl+U)">
                                <i class="fas fa-underline"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('strikeThrough')" title="Strikethrough">
                                <i class="fas fa-strikethrough"></i>
                            </button>
                        </div>

                        <div class="toolbar-separator"></div>

                        <!-- Heading Group -->
                        <div class="editor-group">
                            <select class="editor-dropdown" onchange="formatHeading(this.value)" title="Text Style">
                                <option value="">Normal Text</option>
                                <option value="h1">Heading 1</option>
                                <option value="h2">Heading 2</option>
                                <option value="h3">Heading 3</option>
                            </select>
                        </div>

                        <div class="toolbar-separator"></div>

                        <!-- List Group -->
                        <div class="editor-group">
                            <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')" title="Bullet List">
                                <i class="fas fa-list-ul"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')" title="Numbered List">
                                <i class="fas fa-list-ol"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('indent')" title="Increase Indent">
                                <i class="fas fa-indent"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('outdent')" title="Decrease Indent">
                                <i class="fas fa-outdent"></i>
                            </button>
                        </div>

                        <div class="toolbar-separator"></div>

                        <!-- Alignment Group -->
                        <div class="editor-group">
                            <button type="button" class="editor-btn" onclick="formatText('justifyLeft')" title="Align Left">
                                <i class="fas fa-align-left"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('justifyCenter')" title="Align Center">
                                <i class="fas fa-align-center"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('justifyRight')" title="Align Right">
                                <i class="fas fa-align-right"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('justifyFull')" title="Justify">
                                <i class="fas fa-align-justify"></i>
                            </button>
                        </div>

                        <div class="toolbar-separator"></div>

                        <!-- Insert Group -->
                        <div class="editor-group">
                            <button type="button" class="editor-btn" onclick="insertLink()" title="Insert Link">
                                <i class="fas fa-link"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('insertHorizontalRule')" title="Insert Horizontal Line">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="insertBlockquote()" title="Insert Quote">
                                <i class="fas fa-quote-left"></i>
                            </button>
                        </div>

                        <div class="toolbar-separator"></div>

                        <!-- Utility Group -->
                        <div class="editor-group">
                            <button type="button" class="editor-btn" onclick="formatText('undo')" title="Undo (Ctrl+Z)">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('redo')" title="Redo (Ctrl+Y)">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button type="button" class="editor-btn" onclick="formatText('removeFormat')" title="Clear Formatting">
                                <i class="fas fa-remove-format"></i>
                            </button>
                        </div>
                    </div>
                    <div id="todoDescription"
                         class="editor-content flex-1"
                         contenteditable="true"
                         placeholder="Enter todo description (optional). You can use the formatting tools above."
                         data-placeholder="Enter todo description (optional). You can use the formatting tools above."></div>
                    <textarea id="todoDescriptionHidden" name="description" style="display: none;"></textarea>
                </div>
                <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Text editor with formatting options. Only first 2 lines will be shown in the todo list.</p>
            </div>

            <div class="flex-shrink-0 flex justify-end space-x-3 pt-4 mt-4">
                <button type="button" onclick="hideModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Cancel
                </button>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-save mr-2"></i>
                    <span id="submitButtonText">Create Todo</span>
                </button>
            </div>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
let todos = @json($todos);
let isEditing = false;
let editingId = null;
let sortable = null;

// Helper function to strip HTML tags for display
function stripHtml(html) {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
}

// Text editor functions
function formatText(command, value = null) {
    document.execCommand(command, false, value);
    document.getElementById('todoDescription').focus();
    updateToolbarState();
}

function formatHeading(tag) {
    if (tag) {
        document.execCommand('formatBlock', false, tag);
    } else {
        document.execCommand('formatBlock', false, 'div');
    }
    document.getElementById('todoDescription').focus();
    updateToolbarState();
}

function insertLink() {
    const selection = window.getSelection();
    const selectedText = selection.toString();
    const url = prompt('Enter the URL:', 'https://');

    if (url && url !== 'https://') {
        if (selectedText) {
            formatText('createLink', url);
        } else {
            const linkText = prompt('Enter link text:', url);
            if (linkText) {
                document.execCommand('insertHTML', false, `<a href="${url}" target="_blank">${linkText}</a>`);
            }
        }
    }
    document.getElementById('todoDescription').focus();
}

function insertBlockquote() {
    const selection = window.getSelection();
    const selectedText = selection.toString();

    if (selectedText) {
        document.execCommand('insertHTML', false, `<blockquote>${selectedText}</blockquote>`);
    } else {
        document.execCommand('insertHTML', false, '<blockquote>Quote text here...</blockquote>');
    }
    document.getElementById('todoDescription').focus();
}

function updateToolbarState() {
    // Update button states based on current selection
    const commands = ['bold', 'italic', 'underline', 'strikeThrough'];

    commands.forEach(command => {
        const button = document.querySelector(`[onclick*="${command}"]`);
        if (button) {
            if (document.queryCommandState(command)) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        }
    });
}

function syncEditorContent() {
    const editor = document.getElementById('todoDescription');
    const hiddenField = document.getElementById('todoDescriptionHidden');
    hiddenField.value = editor.innerHTML;
}

document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

function initializePage() {
    renderTodos();
    initializeSortable();
    initializeEventListeners();
}

function initializeEventListeners() {
    // Show completed toggle
    document.getElementById('showCompleted').addEventListener('change', function() {
        renderTodos();
    });

    // Priority filter
    document.getElementById('priorityFilter').addEventListener('change', function() {
        renderTodos();
    });

    // Form submission
    document.getElementById('todoForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitTodo();
    });
}

function initializeSortable() {
    const todoList = document.getElementById('todo-list');
    if (sortable) {
        sortable.destroy();
    }

    sortable = Sortable.create(todoList, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        onEnd: function(evt) {
            reorderTodos();
        }
    });
}

function renderTodos() {
    const todoList = document.getElementById('todo-list');
    const emptyState = document.getElementById('empty-state');
    const showCompleted = document.getElementById('showCompleted').checked;
    const priorityFilter = document.getElementById('priorityFilter').value;

    // Filter todos
    let filteredTodos = todos.filter(todo => {
        if (!showCompleted && todo.is_completed) {
            return false;
        }
        if (priorityFilter !== 'all' && todo.priority !== priorityFilter) {
            return false;
        }
        return true;
    });

    if (filteredTodos.length === 0) {
        todoList.innerHTML = '';
        emptyState.classList.remove('hidden');
        return;
    }

    emptyState.classList.add('hidden');
    todoList.innerHTML = filteredTodos.map(todo => createTodoElement(todo)).join('');

    // Reinitialize sortable after rendering
    initializeSortable();
}

function createTodoElement(todo) {
    const isOverdue = todo.due_date && new Date(todo.due_date) < new Date() && !todo.is_completed;
    const dueDate = todo.due_date ? new Date(todo.due_date).toLocaleDateString() : null;

    return `
        <div class="todo-item ${todo.is_completed ? 'completed' : ''} bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 cursor-move" data-id="${todo.id}">
            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-3 flex-1">
                    <div class="flex items-center pt-1">
                        <input type="checkbox" ${todo.is_completed ? 'checked' : ''}
                               onchange="toggleTodo(${todo.id})"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    </div>

                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-1">
                            <h4 class="todo-title text-lg font-medium text-gray-900 dark:text-white">${todo.title}</h4>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadgeClass(todo.priority)}">
                                ${todo.priority.charAt(0).toUpperCase() + todo.priority.slice(1)}
                            </span>
                            ${isOverdue ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"><i class="fas fa-exclamation-triangle mr-1"></i>Overdue</span>' : ''}
                        </div>

                        ${todo.description ? `<p class="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2" title="${stripHtml(todo.description)}">${stripHtml(todo.description)}</p>` : ''}

                        <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                            ${dueDate ? `<span><i class="fas fa-calendar mr-1"></i>Due: ${dueDate}</span>` : ''}
                            ${todo.completed_at ? `<span><i class="fas fa-check mr-1"></i>Completed: ${new Date(todo.completed_at).toLocaleDateString()}</span>` : ''}
                        </div>
                    </div>
                </div>

                <div class="flex items-center space-x-2 ml-4">
                    <button onclick="editTodo(${todo.id})" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="deleteTodo(${todo.id})" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

function getPriorityBadgeClass(priority) {
    const classes = {
        'high': 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
        'medium': 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
        'low': 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
    };
    return classes[priority] || 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
}

function showCreateModal() {
    isEditing = false;
    editingId = null;
    document.getElementById('modalTitle').textContent = 'Add New Todo';
    document.getElementById('submitButtonText').textContent = 'Create Todo';
    document.getElementById('todoForm').reset();
    document.getElementById('todoId').value = '';

    // Clear editor content
    document.getElementById('todoDescription').innerHTML = '';
    document.getElementById('todoDescriptionHidden').value = '';

    document.getElementById('todoModal').classList.remove('hidden');
}

function editTodo(id) {
    const todo = todos.find(t => t.id === id);
    if (!todo) return;

    isEditing = true;
    editingId = id;
    document.getElementById('modalTitle').textContent = 'Edit Todo';
    document.getElementById('submitButtonText').textContent = 'Update Todo';

    document.getElementById('todoId').value = todo.id;
    document.getElementById('todoTitle').value = todo.title;
    document.getElementById('todoPriority').value = todo.priority;

    // Set editor content
    document.getElementById('todoDescription').innerHTML = todo.description || '';
    document.getElementById('todoDescriptionHidden').value = todo.description || '';

    if (todo.due_date) {
        const date = new Date(todo.due_date);
        document.getElementById('todoDueDate').value = date.toISOString().slice(0, 16);
    }

    document.getElementById('todoModal').classList.remove('hidden');
}

function hideModal() {
    document.getElementById('todoModal').classList.add('hidden');
    document.getElementById('todoForm').reset();

    // Clear editor content
    document.getElementById('todoDescription').innerHTML = '';
    document.getElementById('todoDescriptionHidden').value = '';

    isEditing = false;
    editingId = null;
}

function submitTodo() {
    // Sync editor content to hidden field
    syncEditorContent();

    const formData = new FormData(document.getElementById('todoForm'));
    const data = Object.fromEntries(formData.entries());

    // For updates, use POST with _method spoofing
    const url = isEditing ? `/todo/${editingId}` : '/todo';

    if (isEditing) {
        data._method = 'PUT';
    }

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            hideModal();
            refreshTodos();
        } else {
            showNotification(data.message || 'An error occurred while saving the todo', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while saving the todo', 'error');
    });
}

function toggleTodo(id) {
    fetch(`/todo/${id}/toggle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');

            // Update local todo data
            const todoIndex = todos.findIndex(t => t.id === id);
            if (todoIndex !== -1) {
                todos[todoIndex] = data.todo;
            }

            // Add fade effect for completed items
            const todoElement = document.querySelector(`[data-id="${id}"]`);
            if (data.todo.is_completed && !document.getElementById('showCompleted').checked) {
                todoElement.classList.add('fade-out');
                setTimeout(() => {
                    renderTodos();
                }, 500);
            } else {
                renderTodos();
            }
        } else {
            showNotification(data.message, 'error');
            // Revert checkbox state
            const checkbox = document.querySelector(`[data-id="${id}"] input[type="checkbox"]`);
            checkbox.checked = !checkbox.checked;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while updating the todo', 'error');
        // Revert checkbox state
        const checkbox = document.querySelector(`[data-id="${id}"] input[type="checkbox"]`);
        checkbox.checked = !checkbox.checked;
    });
}

function deleteTodo(id) {
    const todo = todos.find(t => t.id === id);
    if (!todo) return;

    if (!confirm(`Are you sure you want to delete "${todo.title}"?`)) {
        return;
    }

    fetch(`/todo/${id}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');

            // Add fade effect
            const todoElement = document.querySelector(`[data-id="${id}"]`);
            todoElement.classList.add('fade-out');

            setTimeout(() => {
                refreshTodos();
            }, 500);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while deleting the todo', 'error');
    });
}

function reorderTodos() {
    const todoElements = document.querySelectorAll('#todo-list .todo-item');
    const todoIds = Array.from(todoElements).map(el => parseInt(el.dataset.id));

    fetch('/todo/reorder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ todo_ids: todoIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            refreshTodos();
        } else {
            showNotification(data.message, 'error');
            renderTodos(); // Revert to original order
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while reordering todos', 'error');
        renderTodos(); // Revert to original order
    });
}

function refreshTodos() {
    const showCompleted = document.getElementById('showCompleted').checked;
    const priority = document.getElementById('priorityFilter').value;

    const params = new URLSearchParams({
        show_completed: showCompleted,
        priority: priority
    });

    fetch(`/todo/api/list?${params}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            todos = data.todos;
            renderTodos();
        }
    })
    .catch(error => {
        console.error('Error refreshing todos:', error);
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 transform translate-x-full`;

    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
    notification.className += ` ${bgColor} text-white`;

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('todoModal');
    if (e.target === modal) {
        hideModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        hideModal();
    }
});

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    renderTodos();
    initializeSortable();

    // Add event listener to sync editor content
    const editor = document.getElementById('todoDescription');
    if (editor) {
        editor.addEventListener('input', syncEditorContent);
        editor.addEventListener('paste', function() {
            setTimeout(syncEditorContent, 10);
        });

        // Update toolbar state on selection change
        editor.addEventListener('mouseup', updateToolbarState);
        editor.addEventListener('keyup', updateToolbarState);
        editor.addEventListener('focus', updateToolbarState);

        // Handle keyboard shortcuts
        editor.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'b':
                        e.preventDefault();
                        formatText('bold');
                        break;
                    case 'i':
                        e.preventDefault();
                        formatText('italic');
                        break;
                    case 'u':
                        e.preventDefault();
                        formatText('underline');
                        break;
                    case 'z':
                        if (e.shiftKey) {
                            e.preventDefault();
                            formatText('redo');
                        } else {
                            e.preventDefault();
                            formatText('undo');
                        }
                        break;
                    case 'y':
                        e.preventDefault();
                        formatText('redo');
                        break;
                }
            }
        });
    }
});
</script>
@endsection
