<?php

namespace Plugins\Todo\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

class Todo extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'is_completed',
        'priority',
        'due_date',
        'sort_order',
        'user_id',
        'completed_at'
    ];

    protected $casts = [
        'is_completed' => 'boolean',
        'due_date' => 'datetime',
        'completed_at' => 'datetime',
        'sort_order' => 'integer'
    ];

    /**
     * Get the user that owns the todo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only incomplete todos
     */
    public function scopeIncomplete($query)
    {
        return $query->where('is_completed', false);
    }

    /**
     * Scope to get only completed todos
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    /**
     * Scope to order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }

    /**
     * Scope to filter by priority
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Get the next sort order for a user
     */
    public static function getNextSortOrder($userId): int
    {
        return self::where('user_id', $userId)->max('sort_order') + 1;
    }

    /**
     * Mark todo as completed
     */
    public function markCompleted(): void
    {
        $this->update([
            'is_completed' => true,
            'completed_at' => now()
        ]);
    }

    /**
     * Mark todo as incomplete
     */
    public function markIncomplete(): void
    {
        $this->update([
            'is_completed' => false,
            'completed_at' => null
        ]);
    }

    /**
     * Get priority color class
     */
    public function getPriorityColorClass(): string
    {
        return match($this->priority) {
            'high' => 'text-red-600 dark:text-red-400',
            'medium' => 'text-yellow-600 dark:text-yellow-400',
            'low' => 'text-green-600 dark:text-green-400',
            default => 'text-gray-600 dark:text-gray-400'
        };
    }

    /**
     * Get priority badge class
     */
    public function getPriorityBadgeClass(): string
    {
        return match($this->priority) {
            'high' => 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
            'medium' => 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
            'low' => 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
            default => 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
        };
    }

    /**
     * Check if todo is overdue
     */
    public function isOverdue(): bool
    {
        return $this->due_date && $this->due_date->isPast() && !$this->is_completed;
    }
}
