<?php

namespace Plugins\Todo\Seeds;

use Illuminate\Database\Seeder;
use Plugins\Todo\Models\Todo;
use App\Models\User;

class TodoSampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user (admin) or create one if none exists
        $user = User::first();
        
        if (!$user) {
            $this->command->warn('No users found. Please create a user first.');
            return;
        }

        // Clear existing todos for this user
        Todo::where('user_id', $user->id)->delete();

        $sampleTodos = [
            [
                'title' => 'Review project requirements',
                'description' => 'Go through the project specifications and identify key deliverables',
                'priority' => 'high',
                'due_date' => now()->addDays(2),
                'is_completed' => false,
                'sort_order' => 1,
            ],
            [
                'title' => 'Set up development environment',
                'description' => 'Install necessary tools and configure the development workspace',
                'priority' => 'high',
                'due_date' => now()->addDays(1),
                'is_completed' => true,
                'completed_at' => now()->subHours(2),
                'sort_order' => 2,
            ],
            [
                'title' => 'Design database schema',
                'description' => 'Create ERD and define table structures for the application',
                'priority' => 'medium',
                'due_date' => now()->addDays(3),
                'is_completed' => false,
                'sort_order' => 3,
            ],
            [
                'title' => 'Implement user authentication',
                'description' => 'Build login, registration, and password reset functionality',
                'priority' => 'medium',
                'due_date' => now()->addDays(5),
                'is_completed' => false,
                'sort_order' => 4,
            ],
            [
                'title' => 'Create API documentation',
                'description' => 'Document all API endpoints with examples and response formats',
                'priority' => 'low',
                'due_date' => now()->addDays(7),
                'is_completed' => false,
                'sort_order' => 5,
            ],
            [
                'title' => 'Write unit tests',
                'description' => 'Implement comprehensive test coverage for core functionality',
                'priority' => 'medium',
                'due_date' => now()->addDays(6),
                'is_completed' => false,
                'sort_order' => 6,
            ],
            [
                'title' => 'Update team on progress',
                'description' => 'Send weekly status update to stakeholders',
                'priority' => 'low',
                'due_date' => now()->addDays(1),
                'is_completed' => true,
                'completed_at' => now()->subDays(1),
                'sort_order' => 7,
            ],
            [
                'title' => 'Optimize database queries',
                'description' => 'Review and improve slow queries for better performance',
                'priority' => 'medium',
                'due_date' => now()->addDays(4),
                'is_completed' => false,
                'sort_order' => 8,
            ],
            [
                'title' => 'Deploy to staging environment',
                'description' => 'Set up staging server and deploy latest version for testing',
                'priority' => 'high',
                'due_date' => now()->addDays(8),
                'is_completed' => false,
                'sort_order' => 9,
            ],
            [
                'title' => 'Conduct security audit',
                'description' => 'Review application for potential security vulnerabilities',
                'priority' => 'high',
                'due_date' => now()->addDays(10),
                'is_completed' => false,
                'sort_order' => 10,
            ],
        ];

        foreach ($sampleTodos as $todoData) {
            Todo::create(array_merge($todoData, ['user_id' => $user->id]));
        }

        $this->command->info('Sample todo data created successfully!');
        $this->command->info('Created ' . count($sampleTodos) . ' sample todos for user: ' . $user->email);
    }
}
